.dashboard-centre {
  padding: 15px;
  background: #fff;

  .dashboard-add {
    text-align: center;

    .add-icon {
      padding-top: 40px;
      height: 180px;
      font-size: 80px;
      color: #ddd;
      box-sizing: border-box;
    }

    .bottom-text {
      padding: 14px;
    }

    .bottom-items {
      padding-top: 14px;
      border-top: 1px solid #ccc;
      opacity: 0;
      transition: opacity 2s;

      .bottom-btn {
        display: inline-block;
        width: 50%;
        box-sizing: border-box;
      }
    }

    &:hover {
      background: #ddd;

      .add-icon {
        color: #fff;
      }

      .bottom-text {
        display: none;
      }

      .bottom-items {
        opacity: 1;
      }
    }
  }


  .dashboard-card {
    margin-top: 15px;
    height: 230px;


    .bottom {
      margin-top: 13px;
      min-width: 155px;
    }

    .time {
      font-size: 13px;
      color: #999;
    }

    .operate-btn-g {
      float: right;

      .button {
        opacity: 0;
        padding: 2px;
        line-height: 12px;
        transition: opacity 1s;
      }
    }

    &:hover {
      .operate-btn-g {
        .button {
          opacity: 1;
        }
      }
    }
  }

  .dashboard-preview {
    position: relative;
    height: 150px;
    background: rgb(240, 240, 240);

    > img {
      position: absolute;
      width: 100%;
      max-height: 150px;
      object-fit: contain;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
