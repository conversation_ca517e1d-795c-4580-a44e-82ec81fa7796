export default {
  inject: ['context'],
  data() {
    return {
      animate: false,
    };
  },
  computed: {
    appearance() {
      return (this.context && this.context.coreData && this.context.coreData.appearance) || {};
    },
    mode() {
      return this.appearance.mode;
    },
    color() {
      return this.appearance.color || [];
    },
    primaryColor() {
      return this.color[0] || '#034E9B';
    },
    fontColor() {
      return this.appearance.style.color || '#000';
    },
  },
};
