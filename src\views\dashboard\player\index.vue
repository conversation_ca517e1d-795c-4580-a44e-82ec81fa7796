<template>
  <div class="dashboard-player" :class="[theme, mode, coreData.custom.className]" :style="{ overflow: coreData.fitMode === 0 ? 'auto' : 'hidden' }">
    <Layout ref="layout" :dashboard="coreData" :panels="panels">
      <Particles v-if="animate && coreData.appearance.background.dynamic == 1" />
    </Layout>
    <div v-if="coreData.tools.enable" class="dashboard-tools">
      <!--          <el-tooltip class="item" effect="dark" :content="coreCs.alarm.enable ? '关闭事件' : '开启事件'" placement="bottom">-->
      <!--            <el-link :underline="false" @click="coreCs.alarm.enable = !coreCs.alarm.enable">-->
      <!--              <i :class="coreCs.alarm.enable ? 'el-icon-message-solid' : 'el-icon-bell'"></i>-->
      <!--            </el-link>-->
      <!--          </el-tooltip>-->
      <el-tooltip class="item" effect="dark" :content="animate ? '关闭动效' : '开启动效'" placement="bottom">
        <el-link :underline="false" @click.native="checkAnimate">
          <i :class="animate ? 'el-icon-video-pause' : 'el-icon-video-play'"></i>
        </el-link>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" :content="fullscreen ? '退出全屏' : '进入全屏'" placement="bottom">
        <el-link :underline="false" @click.native="checkFullScreen">
          <svg-icon :icon-class="fullscreen ? 'exit-fullscreen' : 'fullscreen'" />
        </el-link>
      </el-tooltip>
    </div>
    <FrameDialog />
  </div>
</template>

<script>
import NoSleep from 'nosleep.js';
import { addListener, removeListener } from 'resize-detector';
import { debounce } from '@/utils';

import Layout from './Layout';
import core from './core';

import Particles from '@/components/Particles';
import log from '@/mixins/log';
import FrameDialog from '@/dialogs/FrameDialog.vue';

export default {
  components: { FrameDialog, Layout, Particles },
  mixins: [core, log],
  data() {
    return {
      animate: false,
      fullscreen: false,
    };
  },
  watch: {
    'coreData.autoRefresh': {
      immediate: true,
      handler(val) {
        if (val && !isNaN(val)) {
          this.autoInterval = setInterval(() => {
            this.getViews().forEach((view) => {
              view.load();
            });
          }, val);
        } else {
          clearInterval(this.autoInterval);
        }
      },
    },
  },
  beforeCreate() {
    this.loading = this.$loading({
      lock: true,
      text: '看板加载中...',
      spinner: 'el-icon-loading',
      customClass: 'dashboard-loading',
      background: '#2b2b2b',
    });
    this.noSleep = new NoSleep();
    this.noSleep.enable();
  },
  mounted() {
    addListener(this.$el, debounce(this.$refs.layout.updateLayoutSize(), 100));

    this.load().finally(() => {
      setTimeout(() => {
        this.loading.close();
        this.loading = null;

        this.checkAnimate();
      }, 700);
    });
  },
  beforeDestroy() {
    clearInterval(this.autoInterval);
    removeListener(this.$el);
    this.noSleep.disable();
  },
  methods: {
    log(info) {
      if (this.loading) {
        console.log(info);
        this.loading.text = info;
      }
    },
    checkFullScreen() {
      if (this.fullscreen) {
        this.fullscreen = false;
        this.exitFullScreen();
      } else {
        this.fullscreen = true;
        this.enterFullScreen();
      }
    },
    checkAnimate() {
      if (this.animate) {
        this.animate = false;
        this.stopAnimate();
      } else {
        this.animate = true;
        this.startAnimate();
      }
    },
    //全屏
    enterFullScreen() {
      let el = document.documentElement;
      let rfs = el.requestFullScreen || el.webkitRequestFullScreen || el.mozRequestFullScreen || el.msRequestFullScreen,
        wscript;

      if (rfs && typeof rfs != 'undefined') {
        rfs.call(el);
      } else if (typeof window.ActiveXObject != 'undefined') {
        // eslint-disable-next-line no-undef
        wscript = new ActiveXObject('WScript.Shell');
        if (wscript) {
          wscript.SendKeys('{F11}');
        }
      }
      this.updateLayoutSize();
    },
    exitFullScreen() {
      let el = document,
        cfs = el.cancelFullScreen || el.webkitCancelFullScreen || el.mozCancelFullScreen || el.exitFullScreen,
        wscript;

      if (cfs && typeof cfs != 'undefined') {
        cfs.call(el);
      } else if (typeof window.ActiveXObject != 'undefined') {
        // eslint-disable-next-line no-undef
        wscript = new ActiveXObject('WScript.Shell');
        if (wscript != null) {
          wscript.SendKeys('{F11}');
        }
      }
      this.updateLayoutSize();
    },
    startAnimate() {
      this.getViews().forEach((view) => {
        view.startAnimate();
      });
    },
    stopAnimate() {
      this.getViews().forEach((view) => {
        view.stopAnimate();
      });
    },
    getViews() {
      return this.$refs.layout.getViews();
    },
    updateLayoutSize() {
      setTimeout(() => {
        this.$refs.layout.updateLayoutSize();
      }, 0);
    },
  },
};
</script>
