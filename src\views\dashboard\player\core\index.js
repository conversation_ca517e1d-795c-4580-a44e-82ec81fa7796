import { compare } from 'compare-versions';
import { insertCss } from 'sau-insert-css';

import Result from '@/utils/result';
import version from '@/utils/version';
import { copyObjectByProperties, extend } from '@/utils';

import Dashboard from '@/models/Dashboard';
import Panel from '@/models/Panel';

import layout from './layout';

import { getVBoard } from '@/api';
import { clearThemeChart, getThemePresetStyle, setThemeChart } from '@/preset/dashboard/themes';
import Appearance from '@/models/Appearance';

export default {
  props: {
    // 看板Id
    id: {
      type: String,
      default: '',
    },
  },
  mixins: [layout],
  data() {
    return {
      coreData: new Dashboard(this.id),
      panels: [],
    };
  },
  provide() {
    return {
      context: this,
    };
  },
  created() {
    // 初始化样式
    this._themeStyle = insertCss('', { id: 'theme' });
    this._customStyle = insertCss('', { id: 'custom' });
  },
  beforeD<PERSON>roy() {
    // 清理样式
    this.clearInsertCss();
    this._themeStyle = null;
    this._customStyle = null;
  },
  computed: {
    available() {
      return Boolean(this.coreData && this.coreData.id);
    },
    appearance() {
      return (this.coreData && this.coreData.appearance) || {};
    },
    mode() {
      return this.appearance.mode;
    },
    theme() {
      return this.appearance.theme;
    },
  },
  methods: {
    checkSaveState() {
      if (this.available) {
        return true;
      } else {
        this.$message({
          message: '请完善信息并发布看板',
          type: 'warning',
        });
        return false;
      }
    },
    open() {
      if (this.checkSaveState()) {
        let routeUrl = this.$router.resolve({
          name: 'dashboard',
          params: { id: this.coreData.id },
        });
        window.open(routeUrl.href, '_blank');
      }
    },
    preview() {
      if (this.checkSaveState()) {
        let routeUrl = this.$router.resolve({
          name: 'preview',
          params: { id: this.coreData.id },
        });
        window.open(routeUrl.href, '_blank');
      }
    },

    // 在1.1.13版本增加了版本检测功能
    // 对历史版本进行数据适配
    adaptFromVersion(data) {
      this.log('检测版本兼容性...');
      //* 注意，这里的版本判断需要从高往低
      if (typeof data.custom.color === 'string') {
        data.custom.color = JSON.parse(data.custom.color);
        data.version = '1.1.12';
      }

      if (typeof data.autofit == 'boolean') {
        data.autofit = 1; // 默认使用比例模式，因为比例不涉及缩放
        data.version = '1.1.0';
      }

      if (data.version && !compare(data.version, version, '=')) {
        // this.$message.warning('注意：程序版本不一致，部分功能不可用');
        this.log(`注意：程序版本不一致（${data.version}），已做兼容处理，可能导致部分功能不可用`, 'warning');
      } else {
        data.version = version;
      }

      return data;
    },
    convertToDashboard(data) {
      const dashboard = copyObjectByProperties(new Dashboard(data.id), data);

      dashboard.fitMode = data.autofit;

      // 工具条
      dashboard.tools.enable = Boolean(data.custom.tools);

      // 布局
      const layout = dashboard.layout;
      layout.colNum = parseInt(data.grid.colNum);
      layout.rowHeight = parseInt(data.grid.rowHeight);

      // 外观样式
      const appearance = dashboard.appearance;

      appearance.theme = data.custom.theme;
      appearance.style = data.fontStyle;
      appearance.mode = data.custom.mode;
      appearance.color = data.custom.color;

      appearance.background.color = data.background;
      appearance.background.image.url = data.backgroundImage.url;
      appearance.background.image.repeat = data.backgroundImage.repeat;
      appearance.background.image.size = data.backgroundImage.size;
      appearance.background.dynamic = data.custom.dynamic;

      // 自定义
      const custom = dashboard.custom;

      custom.layoutClass = data.custom.layoutClass || data.custom.className;
      custom.style = data.custom.styles;

      return dashboard;
    },
    convertToPanel(data) {
      if (data) {
        let panel = new Panel(data.id);

        const panelId = panel.id;
        const defaultDataset = data.component?.props?.dataset?.default;

        panel = copyObjectByProperties(panel, data);
        panel.id = panelId;

        if (defaultDataset) {
          const dataset = typeof defaultDataset === 'function' ? defaultDataset() : typeof defaultDataset === 'object' ? defaultDataset : {};
          extend(true, panel.dataset, dataset, data.dataset);
        } else {
          extend(true, panel.dataset, data.dataset);
        }

        if (panel.dataset && !panel.dataset.dataSource && panel.dataset.api) {
          panel.dataset.dataSource = panel.dataset.api;
          delete panel.dataset.api;
          this.log('检测到旧版本数据源配置，已完成格式转换');
        }

        return panel;
      } else {
        throw new Error('面板数据为空，无法转换');
      }
    },

    loadDashboardData(data) {
      return new Promise((resolve, reject) => {
        const dashboardId = this.coreData.id;

        if (data) {
          resolve(Result.success(data));
        } else if (dashboardId) {
          getVBoard(dashboardId)
            .then(({ data }) => {
              if (data) {
                resolve(Result.success(data));
              } else {
                reject(Result.error('此看板不存在，请发布看板'));
              }
            })
            .catch(() => {
              reject(Result.error('获取数据失败'));
            });
        } else {
          // 没有看板数据和看板id则认为是新看板
          resolve(Result.success());
        }
      });
    },

    load(dashboard) {
      this.log('看板加载中...');
      return this.loadDashboardData(dashboard)
        .then(({ data }) => {
          if (data) {
            this.clearPanels();

            // 对历史版本数据进行兼容性适配
            this.adaptFromVersion(data);
            // 将数据转换为看板对象
            const newDashboard = this.convertToDashboard(data);

            // 触发看板数据加载事件
            this.$emit('onLoad', newDashboard);

            this.coreData = newDashboard;

            // 更新样式
            this.updateThemeStyle();
            this.updateThemeChart();
            this.updateCustomStyle();

            this.$nextTick(() => {
              // 加载面板
              data.elements.forEach((e) => {
                this.addPanel(e);
              });

              // 触发看板数据加载完成事件
              this.$emit('onLoaded', newDashboard);
              this.log('看板加载完成');
            });
          }
          return data;
        })
        .catch((result) => {
          if (result instanceof Result) {
            this.$router.push('/designerx', () => {
              this.$message.warning(result.msg);
            });
          } else {
            console.error(result);
          }
        });
    },

    addPanel(panel) {
      this.$emit('onPanelLoad', panel);
      this.log('加载面板：' + (panel.name || '-'));
      if (!(panel instanceof Panel)) {
        panel = this.convertToPanel(panel);
      }
      this.panels.push(panel);
      this.$emit('onPanelLoaded', panel);
    },
    deletePanel(panel) {
      this.panels.splice(
        this.panels.findIndex((item) => item.id === panel.id),
        1
      );
      this.log('删除面板：' + panel.name);
    },
    clearPanels() {
      this.panels = [];
    },
    reset() {
      this.coreData = new Dashboard();
      this.clearPanels();
    },

    // 样式管理 start
    clearInsertCss() {
      this._themeStyle.clear();
      this._customStyle.clear();
    },
    updateTheme() {
      this.updateThemeData();
      this.updateThemeStyle();
      this.updateThemeChart();

      this.log('更新主题');
    },
    // 设置主题数据
    updateThemeData() {
      const { theme } = this.coreData.appearance;

      const appearance = new Appearance();
      appearance.theme = theme;
      extend(true, this.coreData.appearance, appearance);
    },
    // 设置主题样式
    updateThemeStyle() {
      const { theme } = this.coreData.appearance;

      const style = getThemePresetStyle(theme);
      this._themeStyle.clear();
      this._themeStyle.add(style);
    },
    // 设置主题图表
    updateThemeChart() {
      const { theme } = this.coreData.appearance;

      clearThemeChart();
      setThemeChart(theme);
    },

    updateCustomStyle() {
      this._customStyle.clear();
      this._customStyle.add(this.coreData.custom.style);
      // this.log("更新全局自定义样式");
    },
    // 样式管理 end
  },
  beforeRouteUpdate(to, from, next) {
    this.coreData.id = to.params.id;
    this.refresh(true);
    next();
  },
  beforeRouteLeave(to, from, next) {
    this.clearInsertCss();
    next();
  },
};
