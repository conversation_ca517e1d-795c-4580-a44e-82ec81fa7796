/**
 * @Description: axios实例，如果过滤在自己页面内添加，如src/pages/aoxun/utils/request.js
 * <AUTHOR> <<EMAIL>>
 * @date 2023/8/2
 */
import axios from 'axios';
import Message from '@/utils/message';
import { HTTP_STATUS_CODES } from '@/constant/http';

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 15000,
  timeoutErrorMessage: '请求超时，请稍后再试。',
  headers: { 'Content-Type': 'application/json' },
});

// 添加响应拦截器
service.interceptors.response.use(
  function ({ data }) {
    return data;
  },
  (error) => {
    if (error.response?.status) {
      const code = 'CODE_' + error.response.status;
      if (code === 'CODE_500') {
        error.message = '服务器内部错误。';
      } else if (code === 'CODE_404') {
        error.message = '服务器请求不存在。';
      } else {
        error.message = HTTP_STATUS_CODES[code];
      }
    }
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

/**
 * 假的Promise,用来截断流
 */
const fakePromise = {
  then() {},
  catch() {},
  finally() {},
};

/**
 * 数据验证
 * @param type 验证类型
 */
async function validate(type) {
  const result = await this;

  // 判断是否为约定的result数据格式
  if (typeof result.code === 'number' && result.msg !== undefined) {
    // 结果不为0,则提醒
    if (result.code !== 0) {
      let message;

      if (typeof type === 'string') {
        message = type + '失败,请稍后再试';
      } else if (typeof type === 'function') {
        message = type(result);
      } else {
        message = '操作失败,请稍后再试';
      }

      Message({ message, type: 'warning', duration: 5 * 1000 });

      // 验证不通过且已页面提醒,返回一个假的Promise
      return fakePromise;
    }
    return result;
  }
}

export default function () {
  const promise = service.apply(this, arguments);
  promise.validate = validate;
  return promise;
}
