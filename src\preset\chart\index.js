import { extend } from '@/utils';
import Dashboard from '@/models/Dashboard';

const filters = new Map();

export function getOptionFilter(type) {
  return filters.get(type);
}

export function setOptionFilter(type, filter) {
  if (type && typeof filter === 'function') {
    filters.set(type, filter);
  } else {
    throw new Error('不合规的图表过滤器');
  }
}

export function clearOptionFilter() {
  filters.clear();
}

export const optionLoader = {};

const requireContext = require.context('./defaults', false, /\.js$/);
requireContext.keys().forEach((key) => {
  const defaultOption = requireContext(key).default;
  key = key.replace(/^.+\/([^/]+)\.js/, '$1');
  optionLoader[key] = function (data) {
    const preset = {
      backgroundColor: 'transparent',
      dataset: {
        dimensions: ['name', 'value'],
        source: [],
      },
    };

    extend(true, preset, defaultOption);

    if (data instanceof Dashboard) {
      const {
        appearance: { theme, color, style },
      } = data;
      preset.color = color;
      (preset.textStyle = preset.textStyle || {}).color = style?.color || '#000';

      const doFilter = getOptionFilter(theme);

      if (doFilter && typeof doFilter === 'function') {
        extend(true, preset, doFilter(key, data));
      }
    }

    return preset;
  };
});
