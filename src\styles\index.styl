@import 'common/*';

@import 'designer.styl';
@import 'dashboard.styl';
@import 'app.styl';

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}


body {
  font-family: Roboto, Helvetica Neue, Arial, sans-serif;
  line-height: 1.5;
  color: #464c54;
  background-color: #fff;
  width: 100%;
  position: absolute;
}

.clearfix:before, .clearfix:after {
  display: table;
  content: '';
}

.clearfix:after {
  clear: both;
}

:focus {
  outline: none;
}

.sau-dashboard-chart .sau-panel--body {
  height: 100%;
}

.sau-panel .sau-panel--body {
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: visible;
}

.float-left {
  float: left !important;
}

.float-right {
  float: right !important;
}

.float-none {
  float: none !important;
}

.vue-grid-item {
  box-sizing: border-box;
  touch-action: none;
}

.el-message {
  z-index: 999999999 !important;
}

.el-radio.is-bordered .el-radio__label {
  img {
    margin-right: 10px;
    vertical-align: middle;
  }
}

.overview-img {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.bt-separator {
  border-top: 1px solid #aaa;
}

.dashboard-loading .el-icon-loading {
  font-size: 100px;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-enter {
  transform: translateX(10px);
  opacity: 0;
}

.dashboard-config-list {
  list-style: none;
  padding: 0;

  li {
    padding: 3px 5px;
    border-bottom: 0.2px solid #4c4c4c;
    overflow: auto;
  }

  li:hover {
    background: #54595c;
  }


  .page-btn {
    margin: 0 5px;
  }

  .page-btn-add {
    margin: 5px;
    padding: 5px;
    background: transparent;
    color: #fff;
    border-style: dashed;
  }
}
