import Command from '@/mixins/command/Command';

class DeletePanelCommand extends Command {
  name = '删除';

  execute() {
    const selection = this.app.getSelection();
    this.cache = selection;
    this.app.deleteContent(selection);
    return true;
  }

  undo() {
    if (this.cache) {
      this.app.addContent(this.cache);
    }
    return true;
  }

  redo() {
    if (this.cache) {
      this.app.deleteContent(this.cache);
    }
    return true;
  }
}

export default DeletePanelCommand;
