import framework from 'sau-framework';

import DashboardCentre from '@/views/dashboard';

import { getDesignerRoutes } from '@/router';

const routerCentre = framework.routerCentre;

// 重置首页
routerCentre.homepage.meta.fill = false;
routerCentre.homepage.component = DashboardCentre;

const routers = {
  path: '/pages',
  isLayout: true,
  children: [
    {
      path: '/dashboard',
      menuPath: '/dashboard',
      name: 'DashboardCentre',
      component: () => import('@/views/dashboard'),
      props: true,
      meta: {
        title: '看板中心',
        icon: 'example',
      },
    },
    {
      path: '/visualization',
      menuPath: '/visualization',
      name: 'VisualizationCentre',
      component: () => import('@/views/visualization'),
      props: true,
      meta: {
        title: '可视化管理',
        icon: 'dashboard',
      },
    },
    {
      path: '/myComponents',
      menuPath: '/myComponents',
      name: 'MyComponents',
      component: () => import('@/views/myComponents'),
      props: true,
      meta: {
        title: '我的组件',
        icon: 'component',
      },
    },
  ],
};

routerCentre.asyncRoutes.push(routers);
getDesignerRoutes().forEach((r) => routerCentre.asyncRoutes.push(r));
routerCentre.resetRouter();
