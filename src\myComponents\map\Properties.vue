<template>
  <div>
    <el-form-item label="标记管理">
      <el-switch v-model="dataset.tools.marker"></el-switch>
    </el-form-item>
    <el-form-item label="地图上传">
      <el-switch v-model="dataset.tools.upload"></el-switch>
    </el-form-item>
    <el-form-item label="标记查询">
      <el-switch v-model="dataset.markerSearch"></el-switch>
    </el-form-item>
    <SyncDatasetButton :panel="panel" :source="dataset" />
    <!--        <pre style="text-align: left;">{{JSON.stringify(panel.dataset||{},null, 2)}}</pre>-->
  </div>
</template>

<script>
export default {
  props: {
    panel: Object,
    dataset: Object,
  },
};
</script>
