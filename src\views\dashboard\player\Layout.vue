<template>
  <div class="dashboard-layout" :class="dashboard.type == 1 ? 'flow' : 'custom'" :style="layoutStyle">
    <sau-scrollbar v-if="dashboard.type == 1" style="height: 100%">
      <grid-layout
        :layout.sync="panels"
        :col-num="layout.colNum"
        :row-height="layout.rowHeight"
        :is-draggable="false"
        :is-resizable="false"
        :is-mirrored="false"
        :vertical-compact="true"
        :use-css-transforms="true"
        :margin="[10, 10]"
      >
        <grid-item
          v-for="panel in panels"
          :id="idPrefix + panel.id"
          :key="panel.id"
          :x="panel.x"
          :y="panel.y"
          :w="panel.w"
          :h="panel.h"
          :i="panel.id"
        >
          <DashboardView ref="views" :panel="panel"></DashboardView>
        </grid-item>
      </grid-layout>
      <slot></slot>
    </sau-scrollbar>
    <template v-else>
      <div v-for="panel in panels" :id="idPrefix + panel.id" :key="panel.id" class="dashboard-item" :style="panelStyle(panel)">
        <DashboardView ref="views" :panel="panel"></DashboardView>
      </div>
      <slot></slot>
    </template>
  </div>
</template>

<script>
import design from 'sau-design';

import { FIT_MODE, SCOPE } from '@/constant/designer';
import DashboardView from './DashboardView';
import { getImage } from '@/api';

const { GridLayout, GridItem } = design.DGL;

export default {
  name: 'Layout',
  components: { GridLayout, GridItem, DashboardView },
  props: {
    dashboard: {
      type: Object,
      required: true,
      validator: function (value) {
        return value && value.custom;
      },
    },
    panels: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      containerSize: [0, 0],
    };
  },
  computed: {
    idPrefix() {
      return this.dashboard.custom.idPrefix;
    },
    layout() {
      return this.dashboard.layout;
    },
    // 长宽尺寸比例
    sizeRatio() {
      const [cw, ch] = this.containerSize;
      const [w, h] = this.dashboard.size;
      return [cw / w, ch / h];
    },
    // 缩放比例
    ratio() {
      const [xRatio, yRatio] = this.sizeRatio;
      return Math.min(xRatio, yRatio);
    },
    // 填充策略
    fitStrategy() {
      return FIT_MODE.find((m) => this.dashboard.fitMode === m.value) || {};
    },
    layoutStyle() {
      const style = this.dashboard.appearance.style;

      const background = this.dashboard.appearance.background;
      style.backgroundColor = background.color;
      style.backgroundImage = 'url(' + getImage(background.image.url) + ')';
      style.backgroundSize = background.image.size;
      style.backgroundRepeat = background.image.repeat;

      const size = this.fitStrategy.resize === SCOPE.OUTER || this.fitStrategy.resize === SCOPE.ALL ? this.containerSize : this.dashboard.size;
      style.width = size[0] + 'px';
      style.height = size[1] + 'px';

      if (this.fitStrategy.transform === SCOPE.OUTER || this.fitStrategy.transform === SCOPE.ALL) {
        const [cw, ch] = this.containerSize;
        const [w, h] = this.dashboard.size;
        const [xRatio, yRatio] = this.sizeRatio;
        style.transform = 'scale(' + this.ratio + ')';
        style.transformOrigin =
          xRatio > yRatio ? (cw - w * this.ratio) / (1 - this.ratio) / 2 + 'px 0px' : '0px ' + (ch - h * this.ratio) / (1 - this.ratio) / 2 + 'px';
      }
      return style;
    },
  },
  methods: {
    getViews() {
      return this.$refs.views;
    },
    updateLayoutSize() {
      const { clientWidth, clientHeight } = this.$parent.$el;
      this.containerSize = [clientWidth, clientHeight];
      return this.updateLayoutSize;
    },
    panelStyle({ x, y, w, h }) {
      const [xRatio, yRatio] = this.sizeRatio;
      if (this.fitStrategy.resize === SCOPE.INNER || this.fitStrategy.resize === SCOPE.ALL) {
        if (this.type == 1) {
          this.dashboard.layout.rowHeight *= yRatio;
        } else {
          x *= xRatio;
          y *= yRatio;
          w *= xRatio;
          h *= yRatio;
        }
      }

      if (this.fitStrategy.resize === SCOPE.OUTER && this.fitStrategy.transform === SCOPE.INNER) {
        if (this.type == 1) {
          this.dashboard.layout.rowHeight *= yRatio;
        } else {
          x *= xRatio;
          y *= yRatio;
          w *= xRatio;
          h *= yRatio;
        }

        return {
          left: Math.round(x) + 'px',
          top: Math.round(y) + 'px',
          width: Math.round(w) / this.ratio + 'px',
          height: Math.round(h) / this.ratio + 'px',
          transform: `scale(${this.ratio})`,
          transformOrigin: '0 0',
        };
      }

      return {
        left: Math.round(x) + 'px',
        top: Math.round(y) + 'px',
        width: Math.round(w) + 'px',
        height: Math.round(h) + 'px',
      };
    },
  },
};
</script>
