<template>
  <div style="height: 100%; overflow: auto">
    <p>
      <el-page-header content="插件管理" @back="$router.back()"></el-page-header>
    </p>

    <p v-if="prompted">
      <el-alert title="检测到有操作未生效，请及时刷新页面" type="info" @close="prompted = false"></el-alert>
    </p>

    <el-form ref="form" :model="form" label-width="80px" inline size="mini">
      <el-form-item label="插件名称" clearable>
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-select v-model="form.enable" placeholder="请选择" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="启用" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" clearable>
        <el-input v-model="form.desc" clearable></el-input>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="primary" @click="load">搜索</el-button>
        <el-button @click="resetFilter">重置</el-button>
      </el-form-item>
    </el-form>
    <div style="padding: 0 10px">
      <el-button type="info" size="mini" plain :disabled="selection.length !== 1" @click="handleEditButtonClick">编辑</el-button>
      <el-button type="danger" size="mini" plain :disabled="!selection.length > 0" @click="handleDeleteButtonClick">删除</el-button>

      <el-upload
        action=""
        accept=".zip"
        :show-file-list="false"
        :auto-upload="false"
        :on-change="handleUploadPlugIn"
        style="display: inline; margin: 0 10px"
      >
        <el-button type="primary" size="mini" icon="el-icon-upload2"></el-button>
      </el-upload>

      <sau-dialog title="插件信息" :visible.sync="dialogVisible" :force-middle="true" width="500px">
        <el-form ref="form" :model="dialog" label-width="80px" size="mini">
          <el-form-item label="插件名称" clearable>
            <el-input v-model="dialog.name"></el-input>
          </el-form-item>
          <el-form-item label="是否启用">
            <el-select v-model="dialog.enable" placeholder="请选择" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="dialog.desc" type="textarea" :rows="5" placeholder="请输入内容" clearable></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" size="mini" @click="submitDialog">提交</el-button>
          <el-button size="mini" @click="dialogVisible = false">取消</el-button>
        </span>
      </sau-dialog>
    </div>
    <el-table :data="tableData" style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50"></el-table-column>
      <el-table-column type="index" width="50"></el-table-column>
      <el-table-column prop="name" label="插件名称"></el-table-column>
      <el-table-column prop="url" label="URL"></el-table-column>
      <el-table-column prop="enable" label="启用" width="100">
        <template slot-scope="scope">
          <el-switch :value="scope.row.enable === '1'" @change="handleChangePlugInEnable(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column prop="desc" label="备注" width="180"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import { addViewPlugin, batchDelViewPlugin, getViewPluginList, updViewPlugin } from '@/api/plug_in';
import { clonePure } from '@/utils';

export default {
  name: 'PluginManage',
  data() {
    return {
      prompted: false,
      form: {
        name: '',
        enable: '',
        desc: '',
      },
      tableData: [],
      selection: [],

      dialogVisible: false,
      dialog: {},
    };
  },
  mounted() {
    this.load();
  },
  methods: {
    load() {
      getViewPluginList(this.form)
        .validate()
        .then(({ data }) => {
          this.tableData = data;
        });
    },
    submitDialog() {
      updViewPlugin(this.dialog)
        .validate('修改插件信息')
        .then(() => {
          this.$message.success('修改插件信息成功');
          this.dialogVisible = false;
          this.handleDataChanged();
        });
    },
    resetFilter() {
      this.form = {
        name: '',
        enable: '',
        desc: '',
      };
    },
    handleDataChanged() {
      if (!this.prompted) {
        this.$confirm('该操作需要刷新页面后才能生效，是否需要刷新？', '提示', {
          confirmButtonText: '刷新',
          cancelButtonText: '不再提示',
          type: 'warning',
        })
          .then(() => {
            window.location.reload();
          })
          .catch(() => {
            this.load();
            this.prompted = true;
          });
      } else {
        this.load();
      }
    },
    handleUploadPlugIn(file) {
      addViewPlugin(file.raw)
        .validate()
        .then(() => {
          this.$message.success('上传插件成功');
          this.handleDataChanged();
        });
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    handleEditButtonClick() {
      if (this.selection.length === 1) {
        this.dialog = clonePure(this.selection[0]);
        this.dialogVisible = true;
      } else {
        this.$message.info('请选择一项记录');
      }
    },
    handleDeleteButtonClick() {
      if (this.selection.length > 0) {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            batchDelViewPlugin(this.selection)
              .validate('删除我的插件')
              .then(() => {
                this.$message.success('成功删除记录');
                this.handleDataChanged();
              });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除',
            });
          });
      } else {
        this.$message.info('请选择一项记录');
      }
    },
    handleChangePlugInEnable(plugIn) {
      plugIn.enable = plugIn.enable === '1' ? '0' : '1';
      updViewPlugin(plugIn)
        .validate('切换启用状态')
        .then(() => {
          this.handleDataChanged();
        });
    },
  },
};
</script>
