<template>
  <div>
    <el-form-item v-if="typeof dataset.format === 'string'" label="格式化" required>
      <el-input v-model.trim="dataset.format" type="textarea" :rows="5" clearable></el-input>
    </el-form-item>
    <sau-panel title="文字格式" collapsable virtual-scrollbar>
      <TextProperties :props="dataset.style" />
    </sau-panel>
  </div>
</template>

<script>
import TextProperties from '@/views/dashboard/designer/components/controls/properties/TextProperties';

export default {
  components: {
    TextProperties,
  },
  props: {
    panel: Object,
    dataset: Object,
  },
};
</script>
