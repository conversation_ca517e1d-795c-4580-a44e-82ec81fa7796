import request from '@/utils/request';

/**
 * 查询插件信息 分页和不分页
 * @param name
 * @param enable
 * @param desc
 * @param pageNum
 * @param pageSize
 * @returns {*}
 */
export function getViewPluginList({ name, enable, desc, pageNum, pageSize } = { enable: '1' }) {
  const params = {};

  if (name) {
    params.name = name;
  }
  if (enable) {
    params.enable = enable;
  }
  if (desc) {
    params.desc = desc;
  }
  if (pageNum && pageSize) {
    params.pageNum = pageNum;
    params.pageSize = pageSize;
  }

  return request({
    url: '/bh-business/view/plugin/getViewPluginList',
    method: 'get',
    params,
  });
}

export function updViewPlugin(pluginInfo) {
  return request({
    url: '/bh-business/view/plugin/updViewPlugin',
    method: 'post',
    data: pluginInfo,
  });
}

export function batchDelViewPlugin(selection) {
  return request({
    url: '/bh-business/view/plugin/batchDelViewPlugin',
    method: 'post',
    data: {
      ids: '' + selection.map((n) => n.id),
    },
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
  });
}

/**
 * 导入数据脚本
 * @returns {*}
 */
export function addViewPlugin(file) {
  const data = new FormData();
  data.append('file', file);

  return request({
    url: '/bh-business/view/plugin/addViewPlugin',
    method: 'post',
    data,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
  });
}
