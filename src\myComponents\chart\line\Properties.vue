<template>
  <div>
    <el-form-item label="图表类型" style="margin-top: 15px">
      <el-radio-group v-model="type" size="mini" @change="handleTypeChange">
        <el-radio-button label="line">折线图</el-radio-button>
        <el-radio-button label="bar">柱状图</el-radio-button>
      </el-radio-group>
    </el-form-item>
    <sau-panel title="数据源" collapsable virtual-scrollbar>
      <DataSourceProperties :props="dataset.dataSource"></DataSourceProperties>
    </sau-panel>
    <SyncDatasetButton :panel="panel" :source="dataset" @onSyncDataset="onSyncDataset" />
  </div>
</template>

<script>
export default {
  props: {
    panel: Object,
    dataset: Object,
  },
  inject: ['context'],
  data() {
    return {
      type: 'line',
    };
  },
  methods: {
    handleTypeChange(value) {
      const { series } = this.dataset.option;

      if (Array.isArray(series)) {
        series.forEach((item) => (item.type = value));
      } else if (series.type) {
        series.type = value;
      }
    },
    onSyncDataset() {
      this.$emit('panel', 'load');
    },
  },
};
</script>
