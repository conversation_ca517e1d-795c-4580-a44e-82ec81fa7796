import { MessageBox } from 'element-ui';

import Command from '@/mixins/command/Command';
import Message from '@/utils/message';

class ReplacePanelCommand extends Command {
  name = '更换';

  execute() {
    const selection = this.app.getSelection();
    const componentListSelected = this.app.componentListSelected;

    if (selection?.length > 0) {
      if (componentListSelected) {
        const { type, name = '' } = componentListSelected;

        MessageBox.confirm(`将更换所选面板组件为${name}`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            selection.forEach((panel) => {
              // if (panel.type !== type) {
              const { dataset } = this.app.convertToPanel(componentListSelected);
              Object.assign(panel, componentListSelected, { dataset });
              // } else {
              //   Message({
              //     type: 'info',
              //     message: '组件类型相同，无需更换',
              //   });
              // }
            });
            Message({
              type: 'success',
              message: '更换成功!',
            });
          })
          .catch(() => {
            Message({
              type: 'info',
              message: '已取消更换',
            });
          });
      } else {
        this.app.designerData.ctrlView = '1';
        Message({
          type: 'info',
          message: '请选择需要更换的组件',
        });
      }
    } else {
      Message({
        type: 'info',
        message: '请选择需要更换的面板',
      });
    }

    return false;
  }
}

export default ReplacePanelCommand;
