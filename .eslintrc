{
    "root": true,
    // 必须指定解析器，否则错误难消
    "extends": [
        "eslint:recommended",
        "plugin:vue/essential",
        "plugin:vue/recommended",
        "plugin:prettier/recommended", // 它包含了 extends: [ 'prettier' ], plugins: [ 'prettier' ]
        "prettier/vue"
    ],
    "env": {
        // 环境定义了预定义的全局变量。更多在官网查看
        "browser": true,
        "node": true,
        "commonjs": true,
        "amd": true,
        "es6": true,
        "jest": true,
        "jquery": true
    },
    "globals": { },
    "settings": {
        "import/resolver": {
            "alias": {
                "map": [
                    [
                        "@",
                        "./src"
                    ]
                ],
                "extensions": [
                    ".js",
                    ".jsx",
                    ".json"
                ]
            }
        }
    },
    // JavaScript 语言选项
    "parserOptions": {
        "parser": "babel-eslint"
    },
    /**
    *  "off" 或 0 - 关闭规则
    *  "warn" 或 1 - 开启规则，使用警告级别的错误：warn (不会导致程序退出),
    *  "error" 或 2 - 开启规则，使用错误级别的错误：error (当被触发的时候，程序会退出)
    */
    "rules": {
        "no-debugger": 0,
        "no-unused-vars": 0,
        "vue/require-default-prop": 0
    }
}
