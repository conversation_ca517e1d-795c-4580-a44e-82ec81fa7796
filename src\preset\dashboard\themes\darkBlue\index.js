export default {
  mode: 'dark',
  color: ['#33D7F3', '#EF9D4A', '#D2E1FF', '#FE3616', '#1FABFF', '#297AFE', '#F6D154', '#51F4D5'],
  style: {
    color: '#EBFFFF',
  },
  background: {
    color: '#132D46',
    image: {
      repeat: 'no-repeat',
      size: 'cover',
      url: require('@/assets/images/background/bg6.jpg'),
    },
  },
};

export function filterChartOption(type, data) {
  const {
    appearance: { color },
  } = data;

  if (type === 'top') {
    return {
      xAxis: {
        show: true,
        splitLine: {
          show: true,
        },
        axisLabel: {
          show: false,
        },
      },
      yAxis: [],
      series: [
        {
          barWidth: '30%',
          label: {
            formatter: '{b}',
            show: true,
            position: 'insideLeft',
          },
          yAxisIndex: 0,
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              y2: 0,
              x2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'transparent',
                },
                {
                  offset: 1,
                  color: color[0],
                },
              ],
              type: 'linear',
            },
          },
        },
      ],
    };
  }
}
