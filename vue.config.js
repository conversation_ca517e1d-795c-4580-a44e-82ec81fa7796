const webpack = require('webpack');
let path = require('path');
let CopyWebpackPlugin = require('copy-webpack-plugin');
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

// 消除控制台警告
require('events').EventEmitter.defaultMaxListeners = 20;

// 是否是开发环境 development | production
const isDevlopment = process.env.NODE_ENV === 'development';

function resolve(dir) {
  return path.join(__dirname, dir);
}

let outputDir = 'dashboard';
let plugins = [
  new webpack.ProvidePlugin({
    $: 'jquery',
    jQuery: 'jquery',
  }),
];

if (process.env.npm_lifecycle_event !== 'build-lib') {
  outputDir = 'dist';
  plugins.push(
    new CopyWebpackPlugin([
      {
        from: resolve('node_modules/sau-framework/dist'),
        to: path.join(__dirname, outputDir + '/modules/sau-framework'),
      },
    ])
  );
}

plugins.push(
  new CopyWebpackPlugin([
    {
      from: resolve('node_modules/vue/dist/vue' + (isDevlopment ? '' : '.min') + '.js'),
      to: path.join(__dirname, outputDir + '/modules/vue/vue.min.js'),
    },
  ])
);

module.exports = {
  publicPath: isDevlopment ? '/' : './',
  outputDir,
  pages: {
    index: 'src/pages/dashboard/main.js',
    aoxun: 'src/pages/aoxun/main.js',
  },
  filenameHashing: false,
  transpileDependencies: ['sau-design', 'vue-grid-layout'],
  configureWebpack: {
    name: 'dashboard',
    devtool: 'source-map',
    resolve: {
      alias: {
        '@': resolve('src'),
      },
    },
    externals: {
      moment: 'moment',
      'sau-framework': 'sau_framework',
      vue: 'Vue',
    },
    plugins,
  },
  devServer: {
    port: 3031,
    // before: require('./mock/mock-server.js'),
    proxy: [
      {
        context: '/m1/',
        target: 'http://127.0.0.1:4523',
        changeOrigin: true,
        secure: false,
      },
      // {
      //   context: '/app-interface',
      //   target: 'http://192.168.5.123:9090',
      //   changeOrigin: true,
      // },
      {
        context: '/bh-dimensional/',
        target: 'https://192.168.1.179:9090',
        changeOrigin: true,
        secure: false,
      },
      {
        context: '/bh-drm-admin',
        target: 'https://192.168.1.179:9090',
        changeOrigin: true,
        secure: false,
      },
      {
        context: [
          '/bhApp',
          '/public-studio',
          '/fault-studio',
          '/fault-studio-cloud',
          '/receiver',
          '/bh-tpt',

          '/bh-business/',
          // '/bh-dimensional/',

          '/app-interface',
          '/configPicture',

          '/api',
          '/ws',
        ],
        target: 'https://192.168.1.178:9090',
        changeOrigin: true,
        secure: false,
      },
    ].reduce((p, v) => {
      const { context } = v;
      if (Array.isArray(context)) {
        context.forEach((c) => (p[c] = v));
      } else if (typeof context === 'string') {
        p[context] = v;
      }
      return p;
    }, {}),
  },
  lintOnSave: true,
  runtimeCompiler: false,
  productionSourceMap: false,
  css: {
    sourceMap: isDevlopment, // 开发环境下开启方便调试样式
  },
  chainWebpack: (config) => {
    // npm run analyzer 时才开启
    if (process.env.npm_config_report) {
      config.plugin('webpack-bundle-analyzer').use(BundleAnalyzerPlugin); // 分析项目文件大小的插件
    }

    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .tap((options) =>
        Object.assign(options, {
          limit: 1024,
        })
      );

    config.when(!isDevlopment, (config) => {
      config.optimization.minimizer('terser').tap((args) => {
        args[0].parallel = 4;
        args[0].terserOptions.compress.warnings = true;
        args[0].terserOptions.compress.drop_debugger = true;
        args[0].terserOptions.compress.drop_console = true;
        return args;
      });
    });
  },
};
