import request from '@/utils/request';

/**
 * 获取看板列表
 * @returns {*}
 */
export function getVBoards() {
  return request({
    url: '/bh-business/modules/panel/findList',
    method: 'get',
  });
}

/**
 * 获取某看板信息
 * @param id
 * @returns {*}
 */
export function getVBoard(id) {
  return request({
    url: '/bh-business/modules/panel/findList',
    method: 'get',
    async: false,
    params: {
      id,
    },
  }).then((res) => {
    if (res.data.length > 0 && res.data[0]) {
      res.data = res.data[0];
    } else {
      res.data = null;
    }
    return res;
  });
}

/**
 * 保存看板
 * @param data
 * @returns {*}
 */
export function saveVBoard(data) {
  return request({
    url: '/bh-business/modules/panel/save',
    method: 'post',
    data,
  });
}

/**
 * 删除看板
 * @param ids
 * @returns {*}
 */
export function rmVBoard(ids) {
  let data = new FormData();
  data.append('ids', ids);
  return request({
    url: '/bh-business/modules/panel/remove',
    method: 'post',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
    data,
  });
}

/**
 * 导入看板
 * @param partial
 * @param file
 * @returns {*}
 */
export function importVBoard({ partial = '0', file }) {
  const data = new FormData();
  data.append('partial', partial);
  data.append('file', file);
  return request({
    url: '/bh-business/modules/panel/upload',
    method: 'post',
    headers: {
      // 'Content-type': 'application/json;charset=UTF-8',
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
    data,
  });
}

/**
 * 上传文件
 * @param id
 * @param name
 * @param file
 * @returns {*}
 */
export function saveFile({ id = '', name, file }) {
  let data = new FormData();
  data.append('id', id);
  data.append('name', name); //  文件名称，可不传
  data.append('file', file); //  文件数据，最大16M
  return request({
    url: '/bh-business/modules/file/save',
    method: 'post',
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
    data,
  });
}

/**
 * 获取所有可视化
 * @returns {*}
 */
export function getVisualizaions() {
  return request({
    url: '/bh-business/modules/visualizationFolder/findNodeTree',
    method: 'get',
    params: {},
  });
}

/**
 * 获取可视化目录
 * @returns {*}
 */
export function getVisualizaionFolders() {
  return request({
    url: '/bh-business/modules/visualizationFolder/findTree',
    method: 'get',
    params: {},
  });
}

export function getVisualization(params) {
  return request({
    url: '/bh-business/modules/visualizationNode/findList',
    method: 'get',
    params,
  });
}

/**
 * 保存可视化目录
 * @param data
 * @returns {*}
 */
export function saveVisualizaionFolders(data) {
  return request({
    url: '/bh-business/modules/visualizationFolder/save',
    method: 'post',
    data,
  });
}

/**
 * 保存可视化节点
 * @param data
 * @returns {*}
 */
export function saveVisualizationNode(data) {
  return request({
    url: '/bh-business/modules/visualizationNode/save',
    method: 'post',
    data,
  });
}

/**
 * 删除可视化目录
 * @param ids
 * @returns {*}
 */
export function removeVisualizaionFolders(ids) {
  return request({
    url: '/bh-business/modules/visualizationFolder/remove',
    method: 'post',
    data: {
      ids: '' + ids,
    },
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
  });
}

/**
 * 删除可视化节点
 * @param ids
 * @returns {*}
 */
export function removeVisualizationNodes(ids) {
  return request({
    url: '/bh-business/modules/visualizationNode/remove',
    method: 'post',
    data: {
      ids: '' + ids,
    },
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
  });
}

/**
 * 获取图片地址
 * @param url
 * @returns {string|string|*}
 */
export function getImage(url) {
  if (typeof url == 'string') {
    return url.indexOf('/') >= 0 ? url : '/bh-business/modules/file/getImage?id=' + url;
  } else if (url && url.url) {
    return url.url;
  }
  return '';
}

export function saveTdInspectionPath(data) {
  return request({
    url: '/bh-dimensional/dimensional/tdInspectionPath/save',
    method: 'post',
    data,
  });
}

export function findTdInspectionPathList(pid) {
  return request({
    url: '/bh-dimensional/dimensional/tdInspectionPath/findList',
    method: 'get',
    params: {
      tdConfigId: pid,
    },
  });
}

export function saveTdConfig(data) {
  return request({
    url: '/bh-dimensional/dimensional/tdConfig/save',
    method: 'post',
    data,
  });
}
