import Message from '@/utils/message';
import { clearOptionFilter, setOptionFilter } from '@/preset/chart';

export const styles = new Map();

const req = require.context('!!css-loader!stylus-loader!.', true, /\/index\.styl$/);
const requireAll = (requireContext) => requireContext.keys().forEach((n) => styles.set(n.split(/[\\/]/g)[1], '' + requireContext(`${n}`)));
requireAll(req);

export function getThemePreset(themeName) {
  let themePreset = {};

  if (themeName) {
    try {
      themePreset = require(`./${themeName}/index.js`).default;
    } catch (e) {
      console.error(e);
      Message.warning('加载主题失败，请更新软件');
    }
  }

  return themePreset;
}

export function getThemePresetStyle(themeName) {
  return styles.get(themeName) || '';
}

export function setThemeChart(themeName) {
  if (themeName) {
    try {
      const filterChartOption = require(`./${themeName}/index.js`)?.filterChartOption;
      if (filterChartOption) {
        setOptionFilter(themeName, filterChartOption);
      }
    } catch (e) {
      console.error(e);
    }
  }
}

export function clearThemeChart() {
  clearOptionFilter();
}
