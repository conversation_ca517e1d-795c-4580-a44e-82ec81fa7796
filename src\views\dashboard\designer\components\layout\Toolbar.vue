<template>
  <div class="designer-toolbar">
    <el-tooltip class="item" effect="dark" content="新建" placement="right">
      <router-link :to="{ name: 'designerx' }">
        <DButton size="mini" class="dark" @click="context.execute('reset')">
          <svg-icon icon-class="new" />
        </DButton>
      </router-link>
    </el-tooltip>

    <el-tooltip class="item" effect="dark" content="导入" placement="right">
      <el-upload action="" accept=".board" :show-file-list="false" :auto-upload="false" :on-change="handleImportDashboard">
        <DButton size="mini" class="dark">
          <!--        <i class="el-icon-upload2"></i>-->
          <svg-icon icon-class="clippy" />
        </DButton>
      </el-upload>
    </el-tooltip>

    <el-divider></el-divider>

    <el-tooltip class="item" effect="dark" content="移动" placement="right">
      <DButton size="mini" class="dark" :active="checkMode(modeType.MOVE)" @click="setMode(modeType.MOVE)">
        <i class="el-icon-position"></i>
      </DButton>
    </el-tooltip>

    <el-tooltip class="item" effect="dark" content="操作" placement="right">
      <DButton size="mini" class="dark" :active="checkMode(modeType.HANDLE)" @click="setMode(modeType.HANDLE)">
        <i class="el-icon-thumb"></i>
      </DButton>
    </el-tooltip>

    <!--    <DButton size="mini" class="dark">-->
    <!--      <i class="el-icon-crop"></i>-->
    <!--    </DButton>-->
  </div>
</template>

<script>
import design from 'sau-design';
import { DISIGNER_MODE } from '@/constant/designer';

const { DButton } = design;

export default {
  components: {
    DButton,
  },
  inject: ['context'],
  props: {
    mode: {
      type: Number,
      default: DISIGNER_MODE.HANDLE,
    },
  },
  computed: {
    modeType() {
      return DISIGNER_MODE;
    },
  },
  methods: {
    setMode(mode) {
      this.$emit('update:mode', mode);
    },
    checkMode(mode) {
      return this.mode === mode;
    },
    handleImportDashboard(file) {
      this.context.importDashboard(file);
    },
  },
};
</script>
