const name = '獒巡IT大屏';

const template = {
  remark: null,
  name,
  type: 0,
  size: [1920, 1080],
  autofit: 2,
  timezone: 0,
  autoRefresh: 600000,
  editable: true,
  background: '#233D3E',
  fontStyle: {
    color: '#fff',
    textAlign: 'left',
    letterSpacing: '0px',
    fontFamily: "Times, 'Times New Roman', Georgia, serif",
    fontSize: '14px',
    lineHeight: '14px',
    fontWeight: 'normal',
  },
  backgroundImage: {
    size: 'cover',
    repeat: 'no-repeat',
    url: require('@/assets/images/background/bg2.jpg'),
  },
  desc: '',
  custom: {
    mode: 'dark',
    color: ['#60D3C6', '#71A7E3', '#744ADE', '#36CB8E'],
    idPrefix: 'dashboard-panel',
    theme: 'darkGreen',
    styles: '',
    className: '',
    dynamic: '0',
    tools: true,
    version: '2.0.0',
  },
  grid: {
    colNum: '12',
    rowHeight: '25',
  },
  alarm: null,
  elements: [
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '图片',
      type: 'DashboardImage',
      moved: false,
      wrapped: false,
      editable: false,
      visible: true,
      dataset: {
        src: require('./bg_header.png'),
        style: {
          width: '100%',
          height: '100%',
        },
      },
      x: 0,
      y: 0,
      w: 1920,
      h: 100,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '机柜功率Top5排行',
      type: 'DashboardTop',
      moved: false,
      wrapped: true,
      editable: true,
      visible: true,
      dataset: {
        field: {
          category: 'name',
          value: 'value',
        },
        count: 5,
        sort: {
          enable: true,
          orderBy: 'desc',
        },
        dataSource: {
          result: {
            path: ['data'],
            multiple: false,
            type: 'JSON',
          },
          enable: true,
          type: '1',
          url: {
            type: 'GET',
            value: '/app-interface/getIpmiWattsTop.action',
          },
        },
        option: {
          tooltip: {
            formatter: ['b', '&nbsp;&nbsp;&nbsp;&nbsp;', 'value', 'Watts'],
          },
          yAxis: {
            inverse: true,
            type: 'category',
          },
          xAxis: {
            type: 'value',
          },
          series: [{}],
        },
      },
      x: 1378,
      y: 219,
      w: 500,
      h: 350,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '业务统计',
      type: 'aoxun_BusinessStatistics',
      moved: false,
      wrapped: true,
      editable: true,
      visible: true,
      dataset: {},
      x: 1375,
      y: 596,
      w: 505,
      h: 463,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '核心网络',
      type: 'aoxun_CoreNetwork',
      moved: false,
      wrapped: true,
      editable: true,
      visible: true,
      dataset: {},
      x: 579,
      y: 714,
      w: 778,
      h: 347,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '事件展现',
      type: 'aoxun_EventView',
      moved: false,
      wrapped: true,
      editable: true,
      visible: true,
      dataset: {
        eventLevel: '40',
        limit: 20,
        orgs: [],
      },
      x: 18,
      y: 219,
      w: 529,
      h: 459,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '设备温度top排行',
      type: 'DashboardPie',
      moved: false,
      wrapped: true,
      editable: true,
      visible: true,
      dataset: {
        field: {
          category: 'name',
          value: 'value',
        },
        count: 5,
        dataSource: {
          result: {
            path: ['data'],
            multiple: false,
            type: 'JSON',
          },
          enable: true,
          type: '1',
          url: {
            type: 'GET',
            value: '/app-interface/getWebServerTop.action',
          },
        },
        option: {
          series: [
            {
              dimensions: ['name', 'value', 'unit'],
              encode: {
                tooltip: ['value', 'unit'],
              },
            },
          ],
        },
      },
      x: 18,
      y: 712,
      w: 524,
      h: 350,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '看板标题',
      type: 'DashboardText',
      moved: false,
      wrapped: false,
      editable: true,
      visible: true,
      dataset: {
        fit: false,
        className: 'title',
        style: {
          fontFamily: 'LiSu, Noto Sans Lisu Regular, serif',
          color: '#233030',
          textAlign: 'center',
          letterSpacing: '15px',
          fontSize: '40px',
          lineHeight: '50px',
          fontWeight: 'bold',
        },
        text: '可视化V2 IT大屏',
      },
      x: 715,
      y: 33,
      w: 489,
      h: 55,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '中心组件',
      type: 'aoxun_CentralComponent',
      moved: false,
      wrapped: false,
      editable: true,
      visible: true,
      dataset: {},
      x: 459,
      y: 117,
      w: 1000,
      h: 574,
    },
    {
      id: '',
      createTime: null,
      updateTime: null,
      remark: null,
      name: '时间组件',
      type: 'DashboardMiniTimer',
      moved: false,
      wrapped: false,
      editable: true,
      visible: true,
      dataset: {
        format: 'YYYY年MM月DD日 HH:mm:ss',
        startTime: '',
        style: {
          fontFamily: "Times, 'Times New Roman', Georgia, serif",
          color: '#4CF2C0',
          textAlign: 'left',
          letterSpacing: '0px',
          fontSize: '25px',
          lineHeight: '40px',
          fontWeight: 'normal',
        },
        placeholder: 0,
      },
      x: 1670,
      y: 88,
      w: 178,
      h: 87,
    },
  ],
};

export default {
  name,
  template,
  preview: require('./preview.png'),
};
