<template>
  <div class="setting-visualization">
    <div class="form-wrapper">
      <el-form ref="form" :model="item" :rules="rules" label-width="80px" label-suffix="：">
        <!--          <el-col :span="12">-->
        <el-form-item label="名称" prop="name" required>
          <el-input v-model="item.name"></el-input>
        </el-form-item>
        <!--          </el-col>-->
        <!--          <el-col :span="12">-->
        <!--            <el-form-item label="目录">-->
        <!--              <el-cascader-->
        <!--                v-model="item.parent"-->
        <!--                :options="[]"-->
        <!--              ></el-cascader>-->
        <!--            </el-form-item>-->
        <!--          </el-col>-->

        <el-form-item label="描述">
          <el-input v-model="item.desc" type="textarea" :rows="5"></el-input>
        </el-form-item>
        <el-form-item label="模式" prop="mode" required>
          <el-select v-model="item.mode" placeholder="请选择可视化模式">
            <el-option label="拼接" :value="0"></el-option>
            <el-option label="轮播" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="自适应" required>-->
        <!--          <el-switch v-model="item.fit"></el-switch>-->
        <!--        </el-form-item>-->
        <el-form-item label="看板" prop="dashboards" required style="margin-bottom: 0">
          <el-transfer
            v-model="item.dashboards"
            filterable
            :props="{
              key: 'id',
              label: 'name',
            }"
            :titles="['看板源', '已选']"
            filter-placeholder="请输入看板名称"
            :data="vBoards"
          ></el-transfer>

          <!--            :filter-method="filterMethod"-->
        </el-form-item>
      </el-form>
    </div>

    <div class="preview">
      <div class="preview-content">
        <el-carousel v-if="item.mode == 1" height="100%" indicator-position="none">
          <el-carousel-item v-for="d in transferData" :key="d.id">
            <img style="width: 100%; height: 100%" :src="d.src" :title="d.name" alt="预览" />
          </el-carousel-item>
        </el-carousel>
        <div v-else class="splicing-screen">
          <img v-for="d in transferData" :key="d.id" :style="{ width: 100 / transferData.length + '%' }" :src="d.src" :title="d.name" alt="预览" />
        </div>
      </div>
      <div class="preview-text">
        <span>预览</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getVBoards, saveVisualizationNode } from '@/api';

export default {
  name: 'Visualization',

  props: {
    item: {
      type: Object,
      default() {
        return {
          name: '',
          desc: '',
          folder: '',
          dashboards: [],
        };
      },
    },
  },
  data() {
    return {
      vBoards: [],
      rules: {
        name: [
          { required: true, message: '请输入可视化名称', trigger: 'blur' },
          { required: true, message: '请输入可视化名称', trigger: 'change' },
        ],
        mode: [{ required: true, message: '请选择可视化模式', trigger: 'change' }],
        dashboards: [{ type: 'array', required: true, message: '请至少选择一个看板', trigger: 'change' }],
      },
    };
  },
  computed: {
    vBoardsObj() {
      return this.vBoards.reduce((o, cur) => (o[cur.id] = cur) && o, {});
    },
    transferData() {
      const dashboards = this.item.dashboards || [];
      return dashboards.reduce((arr, cur) => {
        const val = this.vBoardsObj[cur];
        if (val) {
          val.src = '/bh-business/modules/file/getImage?id=' + val.id;
          arr.push(val);
        }
        return arr;
      }, []);
    },
  },
  mounted() {
    getVBoards().then(({ data }) => {
      this.vBoards = data;
    });
  },
  methods: {
    submit(callback) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          saveVisualizationNode(this.item).then(({ data }) => callback(data));
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped>
.setting-visualization {
  height: 100%;

  >>> .el-col {
    margin: 0;
    height: 100%;
  }
}

.form-wrapper {
  float: right;
  /* padding: 20px; */
  width: 618px;
  height: 100%;
  overflow: auto;
  /* border-left: 1px solid #ddd; */
}

.preview {
  position: relative;
  padding: 0 20px;
  height: 100%;
  overflow: hidden;

  .preview-content {
    height: 100%;
    background: #000;

    >>> .el-carousel {
      height: 100%;
    }

    .splicing-screen {
      position: relative;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .preview-text {
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
    text-align: center;
    z-index: 100;

    > span {
      padding: 2px 5px;
      border: 1px solid #333;
      background: #fff;
      opacity: 0.8;
    }
  }
}

>>> .el-transfer {
  white-space: nowrap;

  .el-transfer__buttons {
    .el-button {
      display: block;
      margin: 20px 0;
      padding: 5px;
    }
  }
}
</style>
