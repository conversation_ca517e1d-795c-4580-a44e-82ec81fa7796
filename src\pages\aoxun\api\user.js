import framework from 'sau-framework';
import request from '../utils/request';

export function getPublic<PERSON>ey(data) {
  return framework.request({
    url: '/public-studio/getPublicKey.action',
    method: 'post',
    data,
  });
}

export function login(data) {
  return framework.request({
    url: '/public-studio/login.action',
    method: 'post',
    data,
  });
}

export function getInfo(token) {
  return framework.request({
    url: '/public-studio/showPersonalInfo.action',
    method: 'post',
    params: {},
  });
}

export function logout() {
  return framework.request({
    url: '/public-studio/exitLogin.action',
    method: 'post',
  });
}

export function checkIsVerificationPhone(username) {
  return request({
    url: '/public-studio/checkIsVerificationPhone.action',
    method: 'get',
    params: {
      'sysUser.loginName': username,
    },
  });
}

export function sendVerificationCode(username) {
  return framework.request({
    url: '/public-studio/sendVerificationCode.action',
    method: 'get',
    params: {
      'sysUser.loginName': username,
    },
  });
}
