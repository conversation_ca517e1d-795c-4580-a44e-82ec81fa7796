<template>
  <li v-if="subheader" class="designer-subheader-inset">{{ subheader }}</li>
  <li v-else-if="!divider" class="designer-divider"></li>
  <li v-else class="designer-list-item">
    <div class="designer-list-item-content">
      <slot></slot>
    </div>
    <div class="designer-list-item-tools">
      <slot name="icons">
        <i class="el-icon-more"></i>
      </slot>
    </div>
  </li>
</template>

<script>
export default {
  name: 'ComponentsItem',
  props: {
    subheader: {
      type: String,
      default: '',
    },
    divider: {
      type: String,
      default: 'false',
    },
  },
};
</script>

<style lang="stylus" scoped>
$listItemHeight = 50px;

a {
  color: inherit;
  text-decoration: none;
}

/* 列表项 */
.designer-list-item {
  // position: relative;
  height: $listItemHeight;
  padding: 0;
  text-decoration: none;
  font-weight: 400;
  line-height: 20px;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &::after {
    height: 48px;
    visibility: hidden;
    content: ' ';
  }
}

/* 列表项内容 */
.designer-list-item-content {
  padding-top: 14px;
  padding-bottom: 14px;
}

.designer-list-item-tools {
  position: absolute;
  padding-right: 10px;
  top: 0;
  right: -100%;
  min-width: 24px;
  height: 100%;
  color: rgba(0, 0, 0, 0.54);
  line-height: $listItemHeight;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: #111;

  &:before {
    content: '';
    position: absolute;
    left: - $listItemHeight;
    top: 0;
    display: block;
    width: 0;
    height: 0;
    border-bottom: $listItemHeight solid #111;
    border-left: $listItemHeight solid transparent;
  }

  >>> .el-link {
    min-width: 20px;
    color: #038aff;
  }
}

.designer-subheader-inset {
  &::before {
    left: 72px;
  }
}

// 使用副标题时自动添加分割线
.designer-subheader, .designer-subheader-inset {
  &::before {
    position: absolute;
    right: 0;
    left: 0;
    display: block;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.12);
    content: ' ';
  }

  margin-top: 8px;

  &:first-child {
    &::before {
      background-color: transparent;
    }

    // 第一个副标题把列表的 padding-top 抵消掉
    margin-top: -8px;
  }
}

.designer-divider {
  height: 1px;
  margin: -1px 0 0 0;
  border: none;
}

.designer-divider {
  margin-top: 8px;
  margin-bottom: 8px;
}

.designer-divider, .designer-divider-inset {
  background-color: rgba(255, 255, 255, 0.12);
}
</style>
