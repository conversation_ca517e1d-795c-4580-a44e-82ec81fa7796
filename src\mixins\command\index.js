import CommandHistory from './CommandHistory';
import Command from './Command';

export default {
  created() {
    this.clipboard = {
      once: false,
      data: null,
    };
  },
  data() {
    return {
      history: new CommandHistory(),
    };
  },
  methods: {
    executeCommand(command) {
      if (command instanceof Command) {
        command.app = this;
        if (command.execute()) {
          this.history.push(command);
        }
      } else {
        console.warn('Not a command recognized by the program');
      }
    },
    // 撤销
    undo() {
      if (this.history.hasPrevious()) {
        this.history.current().undo();
        this.history.previous();
      }
    },
    // 取消撤销
    redo() {
      if (this.history.hasNext()) {
        this.history.next().redo();
      }
    },
  },
};
