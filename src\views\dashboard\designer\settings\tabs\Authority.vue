<template>
  <el-table :data="tableData" border style="width: 100%">
    <el-table-column prop="role" label="角色" width="200"></el-table-column>
    <el-table-column prop="desr" label="权限描述"></el-table-column>
    <el-table-column align="center" width="200">
      <template slot="header">
        <el-input v-model="search" size="mini" placeholder="输入角色名搜索" />
      </template>
      <template slot-scope="scope">
        <el-button size="mini" @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  data() {
    return {
      search: '',
      tableData: [
        {
          role: '管理员',
          desr: '管理、编辑、查看',
        },
        {
          role: '编辑者',
          desr: '编辑、查看',
        },
        {
          role: '查看者',
          desr: '查看',
        },
      ],
    };
  },
  methods: {
    handleClick(row) {
      console.log(row);
    },
  },
};
</script>
