<template>
  <el-form ref="form" :model="folder" :rules="rules" label-width="100px" label-suffix="：">
    <el-form-item label="目录名称" prop="name" required>
      <el-input v-model="folder.name"></el-input>
    </el-form-item>
    <!--    <el-form-item label="上级目录">-->
    <!--      <el-cascader-->
    <!--        v-model="folder.parent"-->
    <!--        ref="ccc"-->
    <!--        :props="{ label: 'name', value: 'id' }"-->
    <!--        :options="options"-->
    <!--        :clearable="true"-->
    <!--      >-->
    <!--        <template slot-scope="{ node, data }">-->

    <!--          <div @click.stop="folderChanged(node, data)">{{data.name}}</div>-->
    <!--        </template>-->
    <!--      </el-cascader>-->
    <!--    </el-form-item>-->
    <el-form-item label="描述">
      <el-input v-model="folder.desc" type="textarea" :rows="5"></el-input>
    </el-form-item>
  </el-form>
</template>

<script>
import { getVisualizaionFolders, saveVisualizaionFolders } from '@/api';

export default {
  props: {
    folder: {
      type: Object,
      default() {
        return {
          name: '',
          desc: '',
          parent: '',
        };
      },
    },
  },
  data() {
    return {
      options: [
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components',
            },
            {
              value: 'sketch',
              label: 'Sketch Templates',
            },
            {
              value: 'jiaohu',
              label: '组件交互文档',
            },
          ],
        },
      ],
      rules: {
        name: [
          { required: true, message: '请输入目录名称', trigger: 'blur' },
          { required: true, message: '请输入目录名称', trigger: 'change' },
        ],
      },
    };
  },
  mounted() {
    getVisualizaionFolders().then(({ data }) => (this.options = data));
  },
  methods: {
    // folderChanged(node, data) {
    //   this.$refs.ccc.checkedValue = data.id;
    //   // this.$emit('update:folder',Object.assign(this.folder,{
    //   //   parent:data.id
    //   // }))
    // },
    submit(callback) {
      this.$refs.form.validate((valid) => {
        if (valid) {
          saveVisualizaionFolders(this.folder).then(({ data }) => callback(data));
        } else {
          return false;
        }
      });
    },
  },
};
</script>
