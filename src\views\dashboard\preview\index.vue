<template>
  <div>看板预览</div>
  <!--  <layout :class="coreCs.custom.className" :core-cs="coreCs" :panels="panels" :container-size="containerSize">-->
  <!--    <div v-show="showNotice" class="preview-notice">-->
  <!--      <div class="baseInfo">-->
  <!--        <div class="header">-->
  <!--          <router-link :to="{ path: '/' }">-->
  <!--            <span class="board-name">-->
  <!--              {{ coreCs.name }}-->
  <!--            </span>-->
  <!--          </router-link>-->
  <!--          <el-tag type="info" size="mini">-->
  <!--            {{ coreCs.size[0] + '×' + coreCs.size[1] }}-->
  <!--          </el-tag>-->
  <!--          <el-tag v-if="coreCs.autofit" size="mini">自适应</el-tag>-->
  <!--          <a class="btn like" href="/HYNKp/appreciate" style="margin-left: 10px; margin-right: 30px">-->
  <!--            <i class="el-icon-star-off" style="font-size: 12px"></i>-->
  <!--            <span class="appreciateCount">0</span>-->
  <!--          </a>-->
  <!--          <div style="clear: both"></div>-->
  <!--        </div>-->

  <!--        <div class="close" @click="showNotice = false">-->
  <!--          <i style="font-size: 20px" class="el-icon-close"></i>-->
  <!--        </div>-->
  <!--        <div class="des">-->
  <!--          {{ coreCs.desc }}-->
  <!--        </div>-->
  <!--        <div class="btns">-->
  <!--          <router-link :to="{ name: 'designerx', params: { id: vBoardId } }">-->
  <!--            <span class="btn">编辑</span>-->
  <!--          </router-link>-->
  <!--          <router-link :to="{ path: '/' }">-->
  <!--            <span class="btn">更多看板</span>-->
  <!--          </router-link>-->

  <!--          <div style="clear: both"></div>-->
  <!--        </div>-->
  <!--      </div>-->
  <!--      <div class="section">-->
  <!--        <div class="comment">-->
  <!--          <textarea></textarea>-->
  <!--          <button>留言</button>-->
  <!--        </div>-->
  <!--      </div>-->
  <!--      <div style="clear: both"></div>-->
  <!--    </div>-->
  <!--  </layout>-->
</template>

<script>
// import Layout from '../designer/views/Layout';
// import board from '@/views/dashboard/mixins/board';

export default {
  name: 'DashboardPreview',
  // components: { Layout },
  // mixins: [board],
  // data() {
  //   const { clientWidth, clientHeight } = this.$parent.$el || this.$root.$el;
  //
  //   return {
  //     showNotice: true,
  //     containerSize: [clientWidth, clientHeight],
  //   };
  // },
  // beforeCreate() {
  //   this.loading = this.$loading({
  //     lock: true,
  //     text: '加载中...精彩即将呈现',
  //     spinner: 'el-icon-loading',
  //     customClass: 'dashboard-loading',
  //     background: '#2b2b2b',
  //   });
  // },
  // created() {
  //   this.coreCs.autofit = false; // 默认不显示此标签
  // },
  // mounted() {
  //   this.load().finally(() => {
  //     setTimeout(() => {
  //       this.loading.close();
  //     }, 700);
  //   });
  // },
};
</script>

<style lang="stylus" scoped>
>>> .el-tag {
  margin-left: 10px;
  font-weight: normal;
}

.preview-notice {
  position: fixed;
  top: 10px;
  right: 10px;
  z-index: 999999;
  padding: 10px;
  border-radius: 10px;
  border-top: 4px solid #00a7f7;
  font-size: 12px;
  background: #ffffff;
  box-shadow: 1px 6PX 8px #00000017;

  .close {
    position: absolute;
    right: 6px;
    top: 11px;
    cursor: pointer;
    padding: 6px 9px;
    color: #dcdcdc;

    &:hover {
      color: #333;
    }
  }

  .baseInfo {
    margin: 0px;
    padding: 8px;
    margin-bottom: 0px;

    .board-name {
      margin-bottom: 5px;
      font-size: 1.2rem;
      font-weight: bold;
      word-break: break-all;
      color: #333;
    }

    .appreciateCount {
      margin-left: 0.5em;
    }
  }

  .btns {
    clear: both;
    margin: 5px 0px;
  }

  .btn {
    box-sizing: border-box;
    padding: 5px 10px;
    color: #777777;
    text-decoration: none;
    font-size: 12px;
    margin: 0px 5px;
    border-radius: 20px;
    border: 1px solid #Eee;

    &:hover {
      color: #333;
      background: #d3d3d3;
    }
  }

  .des {
    padding: 12px 0px;
    word-break: break-all;
    min-height: 30px;
    margin-left: 5px;
    width: 240px;
    color: #9da0a7;
  }

  .section {
    border-top: 1px dashed #f5f5f5;

    .comment textarea {
      margin: 2px 10px 2px 2px;
      padding: 5px;
      outline: none;
      border: 1px solid #f3f3f3;
      min-width: 236px;
      max-width: 236px;
      border-radius: 6px;
      min-height: 30px;
    }

    .comment button {
      float: right;
      outline: none;
      background: #36a4f5;
      color: #FFF;
      border: none;
      border-radius: 6px;
      width: 45px;
      height: 41px;
      margin-top: 2px;
      cursor: pointer;
    }
  }
}
</style>
