<template>
  <el-table
    class="log-pane"
    :data="logs"
    :show-header="false"
    :row-class-name="({ row }) => row.level + ' ' + (row.detail ? 'expandable' : '')"
    style="width: 100%"
  >
    <el-table-column type="expand" width="30">
      <template slot-scope="props">
        {{ props.row.detail }}
      </template>
    </el-table-column>
    <el-table-column prop="time" label="时间" width="160"></el-table-column>
    <el-table-column prop="message" label="信息"></el-table-column>
  </el-table>
</template>

<script>
export default {
  props: {
    logs: {
      type: Array,
      default() {
        return [];
      },
    },
  },
};
</script>

<style lang="stylus" scoped>
>>>.el-table__row {
  &.primary {
    color: #409eff;
  }

  &.warning {
    color: #e6a23c;
  }

  &.error {
    color: #f56c6c;
  }
}
</style>
