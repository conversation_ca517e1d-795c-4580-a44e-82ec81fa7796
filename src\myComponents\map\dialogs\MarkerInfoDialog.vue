<template>
  <sau-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="500px"
    :force-middle="true"
    :append-to-body="true"
    :destroy-on-close="true"
    @close="cancel"
  >
    <el-form ref="markerInfoForm" :model="markerInfo" :rules="markerInfoRules" label-width="80px">
      <el-form-item label="标记名称" prop="name" required>
        <el-input v-model.trim="markerInfo.name"></el-input>
      </el-form-item>
      <el-form-item label="标记地址">
        <el-input v-model.trim="markerInfo.position"></el-input>
      </el-form-item>
      <el-form-item label="标记描述">
        <el-input v-model.trim="markerInfo.desc" type="textarea" :rows="5"></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <!--如果没有标记id，则取消时删除标记-->
      <el-button v-if="!markerInfo.id" size="mini" @click="hide">取 消</el-button>
      <el-button type="primary" size="mini" @click="submit">确 定</el-button>
    </div>
  </sau-dialog>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '标记信息',
    },
  },
  data() {
    return {
      dialogVisible: false,
      markerInfo: this.createMarker(),
      submitted: false,
      markerInfoRules: {
        name: [
          { required: true, message: '请输入标记名称', trigger: 'blur' },
          // {min: 2,  message: '至少输入 2 个字符', trigger: 'blur'}
        ],
      },
    };
  },
  methods: {
    createMarker() {
      return {
        name: '',
        position: '',
        desc: '',
      };
    },
    show(markerInfo) {
      this.markerInfo = markerInfo || this.createMarker(); // 重置标点信息
      this.dialogVisible = true;
    },
    hide() {
      this.dialogVisible = false;
    },
    cancel() {
      if (!this.submitted) {
        this.$emit('onCancel', this.markerInfo);
      }
      this.submitted = false;
    },
    submit() {
      this.$refs.markerInfoForm.validate((valid) => {
        if (valid) {
          this.$emit('onSubmit', this.markerInfo);
          this.submitted = true;
          this.hide();
        } else {
          this.$message('请根据提示核实标记信息');
          return false;
        }
      });
    },
  },
};
</script>

<style lang="stylus" scoped></style>
