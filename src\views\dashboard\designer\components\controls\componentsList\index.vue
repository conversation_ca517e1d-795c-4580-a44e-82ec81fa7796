<template>
  <sau-scrollbar class="designer-components-list">
    <div class="designer-filter">
      <el-input v-model="input" size="small" placeholder="请输入组件名" suffix-icon="el-icon-search" clearable></el-input>
    </div>
    <el-menu
      class="designer-list"
      background-color="#343638"
      text-color="#eee"
      active-text-color="#64b5f6"
      :default-openeds="openedMenus"
      @select="handleSelect"
    >
      <ComponentsSubList :list="filterList" />
    </el-menu>
  </sau-scrollbar>
</template>

<script>
import ComponentsSubList from './ComponentsSubList';

import { getComponentList } from '@/myComponents';

export default {
  name: 'ComponentsList',
  components: {
    ComponentsSubList,
  },
  provide() {
    return {
      addPanel: this.addPanel,
    };
  },
  data() {
    return {
      input: '',
      openedMenus: ['0'],
      store: getComponentList(),
    };
  },
  computed: {
    filterList() {
      return this.input ? this.filterTree() : this.store;
    },
  },
  methods: {
    filterTree(tree = this.store, filterData = []) {
      for (let i = 0; i < tree.length; i++) {
        const leaf = Object.assign({}, tree[i]);
        const children = [];
        if (tree[i].children?.length > 0) {
          this.filterTree(tree[i].children, children);
          leaf.children = children;
        }
        if (leaf.title.includes(this.input) || children.length > 0) {
          filterData.push(leaf);
        }
      }
      return filterData;
    },
    addPanel(item) {
      const panel = Object.assign({}, item);
      delete panel.id;

      this.$emit('onAddPanel', panel);
    },
    handleSelect(index, indexPath, menu) {
      const selected = menu.$attrs.data;
      this.selected = selected;
      this.$emit('selected', selected);
    },
  },
};
</script>
