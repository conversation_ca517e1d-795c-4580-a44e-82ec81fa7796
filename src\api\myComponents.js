import request from '@/utils/request';

/**
 * 查询我的组件信息 分页和不分页
 * @param name
 * @param desc
 * @param pageNum
 * @param pageSize
 * @param enable
 * @returns {*}
 */
export function getViewComponetsPage({ name, enable, desc, pageNum, pageSize } = { enable: '1' }) {
  const params = {};

  if (name) {
    params.name = name;
  }
  if (enable) {
    params.enable = enable;
  }
  if (desc) {
    params.desc = desc;
  }
  if (pageNum && pageSize) {
    params.pageNum = pageNum;
    params.pageSize = pageSize;
  }

  return request({
    url: '/bh-business/view/componet/getViewComponetsPage',
    method: 'get',
    params,
  });
}

/**
 *
 * @param panel
 * @returns {*}
 */
export function addViewComponet(panel) {
  return request({
    url: '/bh-business/view/componet/addViewComponet',
    method: 'post',
    data: panel,
  });
}

export function updateViewComponet(panel) {
  return request({
    url: '/bh-business/view/componet/updViewComponet',
    method: 'post',
    data: panel,
  });
}

/**
 * 我的组件单个删除接口
 * @param id
 * @returns {*}
 */
export function delViewComponet(id) {
  return request({
    url: '/bh-business/view/componet/delViewComponet',
    method: 'post',
    data: {
      id,
    },
  });
}

/**
 * 我的组件批量删除接口
 * @param ids 用英文逗号隔开
 * @returns {*}
 */
export function batchDelViewComponet(ids) {
  return request({
    url: '/bh-business/view/componet/batchDelViewComponet',
    method: 'post',
    data: {
      ids,
    },
  });
}

/**
 * 我的组件单个修改接口
 * @returns {*}
 */
export function updViewComponet(data) {
  return request({
    url: '/bh-business/view/componet/updViewComponet',
    method: 'post',
    data,
  });
}

/**
 * 删除我的组件项
 * @param items
 */
export function deleteMyComponents(items) {
  let url;
  let data;

  if (items.length >= 0) {
    if (items.length > 1) {
      url = '/bh-business/view/componet/batchDelViewComponet';
      data = {
        ids: '' + items.map(({ id }) => id),
      };
    } else {
      url = '/bh-business/view/componet/delViewComponet';
      data = {
        id: items[0].id,
      };
    }

    return request({
      url,
      method: 'post',
      data,
      headers: {
        'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
      },
    });
  } else {
    throw new Error('items is empty');
  }
}

/**
 * 导出数据脚本
 * @returns {*}
 */
export function exportDataFile(ids = '') {
  window.open('/bh-business/view/componet/exportDataFile?ids=' + ids);
}

/**
 * 导入数据脚本
 * @returns {*}
 */
export function importDataFile(file) {
  const data = new FormData();
  data.append('file', file);

  return request({
    url: '/bh-business/view/componet/importDataFile',
    method: 'post',
    data,
    headers: {
      'Content-type': 'application/x-www-form-urlencoded;charset=utf-8',
    },
  });
}
