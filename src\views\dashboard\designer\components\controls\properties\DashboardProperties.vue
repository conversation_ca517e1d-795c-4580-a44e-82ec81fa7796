<template>
  <div class="designer-properties designer-properties-dashboard">
    <el-form ref="form" :model="dashboard" label-width="80px" label-suffix=" :" size="mini">
      <el-form-item v-if="dashboard.id" :key="dashboard.updateTime" label-width="0px">
        <imgUpload ref="previewImageUploader" :file="previewImg" :on-change="changePreviewImage">
          <div slot-scope="{ file }">
            <span class="el-upload-list__item-actions">
              <template v-if="file.url">
                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-preview" @click="handleDownload(file)">
                  <i class="el-icon-download"></i>
                </span>
              </template>
              <span class="el-upload-list__item-preview" @click="handleSetPreviewImg(file)">
                <i class="el-icon-setting"></i>
              </span>
            </span>
          </div>
        </imgUpload>
      </el-form-item>
      <el-form-item label="视图大小" style="margin-bottom: 5px">
        <el-col :span="9">
          <el-input v-model.number="dashboard.size[0]"></el-input>
        </el-col>
        <el-col class="line" :span="4">-</el-col>
        <el-col :span="9">
          <el-input v-model.number="dashboard.size[1]"></el-input>
        </el-col>
      </el-form-item>
      <el-form-item style="margin-bottom: 2px; color: #8e8e8e">
        <el-col :span="9">
          {{ dashboardRatio[0] }}
        </el-col>
        <el-col class="line" :span="4">×</el-col>
        <el-col :span="9">
          {{ dashboardRatio[1] }}
        </el-col>
      </el-form-item>

      <el-form-item label="适应模式">
        <el-popover placement="bottom-end" width="180">
          <el-radio-group v-model="dashboard.fitMode" size="small">
            <el-radio
              v-for="mode in fitMode"
              :key="mode.value"
              :label="mode.value"
              border
              style="margin: 10px; padding: 10px; height: auto; min-width: 160px"
            >
              <img :src="require('@/assets/images/fit/64/' + mode.icon)" alt="" />
              {{ mode.name }}
            </el-radio>
          </el-radio-group>
          <el-link slot="reference" type="info" :underline="false">{{ fitModeLabel }}</el-link>
        </el-popover>
      </el-form-item>
      <!--            <el-divider content-position="left">背景设置</el-divider>-->

      <el-collapse v-model="activeNames">
        <el-collapse-item title="字体设置" name="2">
          <TextProperties :props="dashboard.appearance.style" />
        </el-collapse-item>
        <el-collapse-item title="背景设置" name="1">
          <BackgroundProperties :props="dashboard.appearance.background" :default-background-images="defaultBackgroundImages"></BackgroundProperties>
          <el-form-item label="动态背景">
            <el-select v-model="dashboard.appearance.background.dynamic" placeholder="请选择重复方式">
              <el-option label="无" value="0"></el-option>
              <el-option label="行星" value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-collapse-item>
        <el-collapse-item title="风格" class="efficiency" name="3">
          <p>全局设置</p>
          <el-form-item label="主  题">
            <el-select v-model="dashboard.appearance.theme" placeholder="请选择看板主题" clearable @change="context.updateTheme()">
              <el-option label="獒巡" value="ax"></el-option>
              <el-option label="墨绿" value="darkGreen"></el-option>
              <el-option label="管蓝" value="darkBlue"></el-option>
              <!--              <el-option-group label="蓝色">-->
              <!--                <el-option label="深蓝" value="blue1"></el-option>-->
              <!--                <el-option label="流线" value="blue2"></el-option>-->
              <!--                <el-option label="渐变" value="blue3"></el-option>-->
              <!--              </el-option-group>-->
            </el-select>
          </el-form-item>
          <el-form-item label="模 式">
            <el-select v-model="dashboard.appearance.mode" placeholder="请选择色彩模式">
              <!--                <el-option label="默认" value=""></el-option>-->
              <el-option label="明亮" value="light"></el-option>
              <el-option label="暗色" value="dark"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="颜色列表">
            <ColorListPicker v-model="dashboard.appearance.color"></ColorListPicker>
          </el-form-item>
          <p>面板设置</p>
          <el-form-item label="主题面板" style="margin-bottom: 5px">
            <el-switch v-model="panelsProp.wrapped" @change="handleQuickWrapped"></el-switch>
          </el-form-item>
        </el-collapse-item>
        <el-collapse-item title="高级" name="4" class="normal-text">
          <div style="padding: 10px">
            <label>自定义样式：</label>
            <el-input
              v-model="dashboard.custom.style"
              type="textarea"
              :autosize="{ minRows: 10, maxRows: 25 }"
              :rows="5"
              @change="context.updateCustomStyle"
            ></el-input>
          </div>
        </el-collapse-item>
      </el-collapse>

      <sau-dialog title="预览图" :visible.sync="dialogVisible" :force-middle="true" :append-to-body="true">
        <img class="overview-img" :src="dialogImageUrl" alt="" style="width: 100%" />
      </sau-dialog>
    </el-form>
  </div>
</template>

<script>
import { imatateDownloadByA } from '@/utils';
import { integerRatio } from '@/utils/calculate';
import layout from '@/views/dashboard/player/core/layout';
import ImgUpload from '@/components/upload/ImgUpload';
import TextProperties from '../properties/TextProperties';
import ColorListPicker from '@/components/ColorListPicker';

import { getImage, saveFile } from '@/api';
import { FIT_MODE, BACKGROUND_IMAGE } from '@/constant/designer';
import BackgroundProperties from './BackgroundProperties';

export default {
  name: 'DashboardProperties',
  components: {
    BackgroundProperties,
    ColorListPicker,
    ImgUpload,

    TextProperties,
  },
  inject: ['context'],
  mixins: [layout],
  props: {
    designer: {
      type: Object,
      require: true,
    },
    dashboard: {
      type: Object,
      require: true,
    },
    panels: {
      type: Array,
      default() {
        return [];
      },
    },
    selection: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',

      activeNames: [],
      fitMode: FIT_MODE,

      defaultBackgroundImages: BACKGROUND_IMAGE,

      panelsProp: {
        wrapped: false,
      },
    };
  },
  computed: {
    previewImg() {
      return {
        name: '预览图',
        url: getImage(this.dashboard.id),
      };
    },
    fitModeLabel() {
      const mode = this.fitMode.find((n) => this.dashboard.fitMode === n.value);
      return mode ? mode.name : '未设置';
    },
    dashboardRatio() {
      return integerRatio(this.dashboard.size[0], this.dashboard.size[1]);
    },
  },

  methods: {
    // Start 预览图
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleDownload({ name = '预览图片.png', url }) {
      imatateDownloadByA(url, name);
    },
    changePreviewImage(file) {
      saveFile({
        id: this.dashboard.id,
        name: 'preview.png',
        file,
      })
        .then(() => {
          this.$message({
            message: '上传预览图成功',
            type: 'success',
          });
        })
        .catch(() => {
          this.$message.error('系统错误：保存预览图异常');
        });
    },
    handleSetPreviewImg() {
      this.$refs.previewImageUploader.select();
    },
    // End 预览图

    handleQuickWrapped(value) {
      this.panels.forEach((panel) => (panel.wrapped = value));
    },
  },
};
</script>
