<template>
  <div>
    <el-form-item label="上传图片" style="margin: 10px 0">
      <el-upload action="" accept=".jpg,.jpeg,.png,.gif" :show-file-list="false" :auto-upload="false" :on-change="handleUploadChange">
        <el-button size="mini" type="primary">点击上传</el-button>
      </el-upload>
    </el-form-item>
    <!--    <el-form-item label="自适应">-->
    <!--      <el-switch v-model="dataset.fit"></el-switch>-->
    <!--    </el-form-item>-->

    <SyncDatasetButton :panel="panel" :source="dataset" />
  </div>
</template>

<script>
import { getImage, saveFile } from '@/api';

export default {
  components: {},
  props: {
    panel: Object,
    dataset: Object,
  },
  methods: {
    handleUploadChange({ raw }) {
      saveFile({ file: raw }).then(({ data }) => (this.dataset.src = getImage(data.id)));
    },
  },
};
</script>
