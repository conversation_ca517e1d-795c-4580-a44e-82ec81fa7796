/**
 * @Description: 定制EL-UI的提示，目的是为了让它不要一次性连续出现多条
 * <AUTHOR> <<EMAIL>>
 * @date 2023/5/29
 */

import { Message as ElMessage } from 'element-ui';
import { isVNode } from 'element-ui/src/utils/vdom';
import { isObject } from 'element-ui/src/utils/types';

let instance;

const Message = function (options = {}) {
  if (typeof options === 'string') {
    options = {
      message: options,
    };
  }
  if (!instance || instance.closed) {
    instance = ElMessage(options);
  }
  Object.assign(instance, options);
  return instance;
};

['success', 'warning', 'info', 'error'].forEach((type) => {
  Message[type] = (options) => {
    if (isObject(options) && !isVNode(options)) {
      return Message({
        ...options,
        type,
      });
    }
    return Message({
      type,
      message: options,
    });
  };
});

Message.close = function () {
  if (instance) {
    instance.close();
  }
};

export default Message;
