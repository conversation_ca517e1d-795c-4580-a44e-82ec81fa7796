import request from '@/utils/request';

function createEmptyData(data) {
  // 获取原数据类型
  const dataType = typeof data;
  // 根据不同的数据类型，返回不同的空值
  switch (dataType) {
    case 'string':
      return '';
    case 'number':
      return 0;
    case 'boolean':
      return false;
    case 'object':
      // 如果是数组，返回空数组
      if (Array.isArray(data)) {
        return [];
      }
      // 如果是对象，返回空对象
      else {
        return {};
      }
    case 'function':
      // 如果是函数，返回一个空函数
      return function () {};
    default:
      // 如果是其他类型，返回 null
      return null;
  }
}

export default {
  // 创建数据源配置模板
  creteDataSourceModel() {
    return {
      enable: false,
      type: '1',
      url: {
        value: '',
        type: 'GET',
      },
      result: {
        type: 'JSON',
        multiple: false,
        path: [],
      },
    };
  },
  inject: ['context'],
  // vue template
  data() {
    return {
      data: [],
    };
  },
  computed: {
    dataSource() {
      return this.dataset.dataSource;
    },
  },
  methods: {
    load() {
      if (this.dataSource) {
        const {
          enable,
          url: { value, type, ...config },
          result: { multiple, path },
        } = this.dataSource;

        if (enable) {
          request({
            url: value,
            method: type,
            ...config,
          })
            .then((result = []) => {
              const resultData = multiple ? path.map((n) => n.reduce((p, k) => p[k], result)) : path.reduce((p, k) => p[k], result);

              // 检测结果与定义的数据类型是否一致
              if (resultData instanceof this.data.constructor) {
                this.data = resultData;
              } else {
                this.data = createEmptyData(this.data);

                // TODO: 暂时抛出异常，版本更新时再设计，是否根据模式来弹框提示
                throw new Error('数据类型不一致，请求结果被舍弃');
              }
            })
            .catch((e) => {
              this.context.log({ message: '加载面板数据失败', detail: e.message, level: 'warning' });
            });
        }
      } else {
        throw new Error('无可用数据源');
      }
    },
  },
};
