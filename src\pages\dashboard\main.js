import Vue from 'vue';
import Router, { RouterView } from 'vue-router';

import './styles/index.styl';

import dashboard from '@/dashboard';

import Homepage from '@/views/homepage';

import { getDesignerRoutes } from '@/router';

Vue.use(Router);
Vue.use(dashboard);

dashboard.ready(function () {
  new Vue({
    router: new Router({
      routes: getDesignerRoutes().concat([
        {
          path: '/',
          name: 'homepage',
          component: Homepage,
          props: true,
          meta: {
            title: '主页',
            icon: 'el-icon-s-home',
          },
        },
        {
          path: '*',
          redirect: '/',
        },
      ]),
    }),
    render: (h) => h(RouterView),
  }).$mount('#app');
});
