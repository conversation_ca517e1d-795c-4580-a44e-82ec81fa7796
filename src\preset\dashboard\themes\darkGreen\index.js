export default {
  mode: 'dark',
  color: ['#60D3C6', '#36CB8E', '#71A7E3', '#744ADE'],
  style: {
    color: '#fff',
  },
  background: {
    color: '#233D3E',
    image: {
      repeat: 'no-repeat',
      size: 'cover',
      url: require('@/assets/images/background/bg2.jpg'),
    },
  },
};

export function filterChartOption(type, data) {
  const {
    appearance: { color },
  } = data;

  if (type === 'top') {
    return {
      xAxis: {
        axisLine: {
          show: false,
        },
      },
      yAxis: [
        {
          inverse: true,
          axisLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          barWidth: '50%',
          label: {
            formatter: '{b}',
            show: true,
            position: 'insideLeft',
          },
          yAxisIndex: 0,
          itemStyle: {
            color: {
              x: 0,
              y: 0,
              y2: 0,
              x2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'transparent',
                },
                {
                  offset: 1,
                  color: color[0],
                },
              ],
              type: 'linear',
            },
          },
        },
      ],
    };
  }
}
