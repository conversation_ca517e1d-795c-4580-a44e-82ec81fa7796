<template>
  <div>
    <ul class="dashboard-config-list">
      <li v-for="(item, index) in dataset.items" :key="index">
        <span v-text="item.name || '未命名'"></span>
        <span style="float: right">
          <el-link class="page-btn" type="primary" :underline="false" icon="el-icon-edit" @click="selected = item"></el-link>
          <el-link class="page-btn" type="danger" :underline="false" icon="el-icon-delete" @click="deleteItem(item, index)"></el-link>
        </span>
      </li>
      <el-button class="page-btn-add" size="mini" plain @click="componentsDialog.visible = true">添加组件</el-button>
    </ul>

    <el-form-item label="自动切换">
      <el-switch v-model="dataset.autoplay"></el-switch>
    </el-form-item>
    <el-form-item label="切换间隔">
      <el-input-number v-model="dataset.interval" controls-position="right" :min="1" style="width: 100px"></el-input-number>
      &nbsp;毫秒
    </el-form-item>
    <el-form-item label="指示器">
      <el-switch v-model="dataset.indicatorPosition" active-value="inside" inactive-value="none"></el-switch>
    </el-form-item>

    <sau-panel v-if="selected" :title="selected.name + '属性'" collapsable>
      <ItemProperties :panel="selected" :dataset="selected.dataset" @panel="$emit('panel', $event)"></ItemProperties>
    </sau-panel>

    <sau-dialog title="添加组件" :visible.sync="componentsDialog.visible" width="450px" top="20%">
      <el-tree
        ref="componentsDialogList"
        :props="{
          label: 'name',
        }"
        :data="componentsDialog.list"
        show-checkbox
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
      ></el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="componentsDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="addItem">确 定</el-button>
      </span>
    </sau-dialog>
  </div>
</template>

<script>
import ItemProperties from './ItemProperties';

import { getComponentList } from '@/myComponents';

export default {
  components: {
    ItemProperties,
  },
  props: {
    panel: {
      type: Object,
    },
    dataset: {
      type: Object,
    },
  },
  data() {
    return {
      componentsDialog: {
        visible: false,
        list: getComponentList(),
      },
      selected: null,
    };
  },
  inject: ['context'],
  mounted() {},
  methods: {
    addItem() {
      this.$refs.componentsDialogList.getCheckedNodes().forEach((item) => {
        item.visible = true;
        item.wrapped = false;
        this.dataset.items.push(this.context.convertToPanel(item));
      });
      this.componentsDialog.visible = false;
      this.$refs.componentsDialogList.setCheckedKeys([]);
    },
    deleteItem(item, index) {
      this.dataset.items.splice(index, 1);
    },
  },
};
</script>
