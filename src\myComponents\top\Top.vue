<template>
  <v-chart v-if="data.length > 0" :option="option" :theme="context.coreData.appearance.mode" autoresize />
  <el-empty v-else></el-empty>
</template>

<script>
import { optionLoader } from '@/preset/chart';
import dataSource from '@/mixins/dataSource';
import { extend } from '@/utils';
import appearance from '@/mixins/appearance';

/** Top排行面板 */
export default {
  inject: ['context'],
  mixins: [appearance, dataSource],
  props: {
    wrapped: Boolean,
    dataset: {
      type: Object,
      default() {
        return {
          dataSource: dataSource.creteDataSourceModel(),
          option: {
            xAxis: {
              type: 'value',
            },
            yAxis: [
              {
                type: 'category',
                inverse: true,
              },
            ],
            series: [
              {
                label: {},
              },
            ],
          },
          count: 5, // 排行数量，默认Top5
          sort: {
            enable: true,
            orderBy: 'desc',
          },
          field: {
            category: 'name',
            value: 'value',
          },
        };
      },
    },
  },
  computed: {
    option() {
      const {
        option,
        count,
        sort,
        dataSource: {
          result: { multiple },
        },
        field: { category, value },
      } = this.dataset;

      let data;

      //! 这里try主要是处理当勾选了多条数据项后，数据并未加载的情况，即 取【type1，type2】两项数据，但是数据是原本的【1,2,3,4...】
      try {
        // TODO 多数据项时先默认用第一项，怎么配置为多项再优化
        data = multiple ? this.data[0] : this.data;
      } catch (e) {
        data = [];
        this.$message.info('检测到数据项变动，请进行数据同步');
      }

      while (data.length < count) {
        data.push({ [category]: Date.now() });
      }

      if (data.length > count) {
        data = data.slice();
        data.splice(count);
      }

      if (sort?.enable) {
        data.sort(function (a, b) {
          return (a[value] - b[value]) * (sort.orderBy === 'asc' ? 1 : -1);
        });
      }

      const result = {
        dataset: {
          source: data,
        },
      };

      if (Array.isArray(option.tooltip?.formatter)) {
        result.tooltip = {
          formatter({ marker, seriesName, name, value }) {
            return option.tooltip.formatter.reduce((pre, param) => {
              if (param === 'marker') {
                return pre + marker;
              } else if (param === 'a') {
                return pre + seriesName;
              } else if (param === 'b') {
                return pre + name;
              } else if (param === 'c') {
                return pre + value;
              } else if (value[param] !== null && value[param] !== void 0) {
                return pre + value[param];
              } else {
                return pre + param;
              }
            }, '');
          },
        };
      }

      if (!Array.isArray(option.yAxis)) {
        option.yAxis = [option.yAxis];
      }

      // ?这是ECharts已知的一个Bug：dataset 在遇到相同名称的数据行时，会将它们视为“同一个数据项”，导致后面的数据覆盖前面的数据
      // fix: 这里通过单独设置yAxis.data来避免
      if (option.yAxis.length === 2) {
        result.yAxis = result.yAxis || [{}, {}];
        if (!result.yAxis[1]) {
          result.yAxis[1] = {};
        }
        result.yAxis[1].data = data?.map((d) => d?.[value]) || [];
      }

      // 加载预设配置并设置
      const preset = optionLoader.top(this.context.coreData);
      preset.series[0].label.color = this.fontColor;

      return extend(true, preset, option, result);
    },
  },
  mounted() {
    this.load();
  },
};
</script>
