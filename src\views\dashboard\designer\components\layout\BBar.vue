<template>
  <div class="designer-bbar">
    <span style="float: left">
      <span>面板总数： {{ panels.length }}</span>
    </span>

    <span v-show="dashboard.updateTime">最后更新时间： {{ dashboard.updateTime }}</span>

    <span style="float: right; line-height: 18px">
      <template v-if="dashboard.type == 0">
        <span class="designer-bbar-position">{{ mouseX }},{{ mouseY }}</span>
        <el-slider v-model="designer.scale" class="bbar-item" :min="10" :max="150" :format-tooltip="formatPercent" input-size="mini"></el-slider>
      </template>
      <el-tooltip class="bbar-item" effect="dark" :content="designer.lock ? '视图已锁定' : '视图未锁定'" placement="top">
        <el-link :underline="false" @click="designer.lock = !designer.lock">
          <i :class="designer.lock ? 'el-icon-lock' : 'el-icon-unlock'"></i>
        </el-link>
      </el-tooltip>
      <el-tooltip class="bbar-item" effect="dark" content="帮助" placement="top">
        <el-link :underline="false" @click="help"><i class="el-icon-question"></i></el-link>
      </el-tooltip>
    </span>
  </div>
</template>

<script>
export default {
  props: {
    designer: {
      type: Object,
      require: true,
    },
    dashboard: {
      type: Object,
      required: true,
      validator: function (value) {
        return value && value.custom;
      },
    },
    panels: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  computed: {
    mouseX() {
      const { scale, mouseX } = this.designer;
      return Math.trunc(mouseX / (scale / 100));
    },
    mouseY() {
      const { scale, mouseY } = this.designer;
      return Math.trunc(mouseY / (scale / 100));
    },
  },
  methods: {
    formatPercent(val) {
      return val + '%';
    },
    help() {
      this.$alert('Ctrl + S 快速保存<br/>切换类型会清空面板', '帮助', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '好',
        type: 'info',
      });
    },
  },
};
</script>
