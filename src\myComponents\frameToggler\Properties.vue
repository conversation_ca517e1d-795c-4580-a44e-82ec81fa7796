<template>
  <div>
    <ul class="dashboard-config-list">
      <li v-for="(page, index) in pages" :key="index">
        <span v-text="page.name || '未命名'"></span>
        <span style="float: right">
          <el-link class="page-btn" type="primary" :underline="false" icon="el-icon-edit" @click="editPage(page)"></el-link>
          <el-link class="page-btn" type="danger" :underline="false" icon="el-icon-delete" @click="deletePage(page, index)"></el-link>
        </span>
      </li>
      <el-button class="page-btn-add" size="mini" plain @click="editPage()">添加页面</el-button>
    </ul>
    <SyncDatasetButton :panel="panel" :source="dataset" @onSyncDataset="onSyncDataset" />
    <sau-dialog :title="dialog.title" :visible.sync="dialog.visible" :append-to-body="true" :force-middle="true" width="30%">
      <el-form ref="form" :model="dialog.page" label-width="120px" label-suffix="：">
        <el-form-item label="页面名称">
          <el-input v-model="dialog.page.name"></el-input>
        </el-form-item>
        <el-form-item label="页面地址">
          <el-input v-model="dialog.page.src"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialog.visible = false">取 消</el-button>
        <el-button type="primary" size="small" @click="submitEditPage()">确 定</el-button>
      </span>
    </sau-dialog>
  </div>
</template>

<script>
export default {
  props: {
    panel: Object,
    dataset: Object,
  },
  data() {
    const dataset = this.dataset;
    return {
      pages: dataset ? dataset.pages : [],
      dialog: {
        title: '',
        visible: false,
        isAdding: true,
        page: {
          name: '',
          src: '',
        },
      },
      syncing: false,
    };
  },
  methods: {
    editPage(pageInfo) {
      if (pageInfo) {
        this.dialog.page = pageInfo;
        this.dialog.isAdding = false;
        this.dialog.title = '修改页面';
      } else {
        this.dialog.page = {
          name: '',
          src: '',
        };
        this.dialog.isAdding = true;
        this.dialog.title = '新增页面';
      }
      this.dialog.visible = true;
    },
    submitEditPage() {
      if (this.dialog.isAdding) {
        this.pages.push(this.dialog.page);
      }
      this.dialog.visible = false;
    },
    deletePage(page, index) {
      this.pages.splice(index, 1);
    },
    onSyncDataset() {
      this.dataset.pages = this.pages.map((i) => ({ ...i }));
    },
  },
};
</script>
