<template>
  <sau-dialog :title="dialog.title" :visible.sync="dialogVisible" :top="dialog.top" :width="dialog.width">
    <Folder v-if="checkIsFolder" ref="content" :folder="item"></Folder>
    <Visualization v-else ref="content" :item="item"></Visualization>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submit">确 定</el-button>
    </span>
  </sau-dialog>
</template>

<script>
import Visualization from '../form/Visualization';
import Folder from '../form/Folder';

export default {
  components: { Visualization, Folder },
  props: {
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    isFolder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  computed: {
    dialog() {
      let info;
      if (this.checkIsFolder) {
        info = {
          title: this.item?.id ? '目录管理' : '新建目录',
          top: '20%',
          width: '500px',
        };
      } else {
        info = {
          title: this.item?.id ? '可视化管理' : '新建可视化',
          top: '10%',
          width: '80%',
        };
      }
      return info;
    },
    checkIsFolder() {
      if (this.item.id) {
        return !Object.prototype.hasOwnProperty.call(this.item, 'mode');
      }
      return this.isFolder;
    },
  },
  methods: {
    show() {
      this.dialogVisible = true;
    },
    submit() {
      this.$refs.content.submit(() => {
        this.dialogVisible = false;
        this.$emit('submited');
      });
    },
  },
};
</script>
