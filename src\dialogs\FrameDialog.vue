<template>
  <sau-dialog ref="dialog" :title="core.title" :visible.sync="dialogVisible" top="5%" width="90%" :modal="false" :close-on-click-modal="false">
    <iframe name="bh-frame-1" width="100%" height="100%" frameborder="0" scrolling="no" :src="core.src"></iframe>
  </sau-dialog>
</template>

<script>
export default {
  name: 'FrameDialog',
  props: {
    url: String,
    preload: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      core: {
        title: '',
        src: this.url,
      },
    };
  },
  mounted() {
    this.$refs.dialog.rendered = true;
    window.addEventListener('message', this.onMessage);
  },
  methods: {
    onMessage(event) {
      const { type, data } = event.data;
      if (type === 'showUrl') {
        this.dialogVisible = true;
        Object.assign(this.core, data);
      }
    },
  },
};
</script>

<style scoped>
>>> .el-dialog__body {
  padding: 0;
}
</style>
