<template>
  <img v-if="dataset.src" class="dashboard-image" :style="dataset.style" :src="dataset.src" />
  <el-empty v-else :image-size="Math.min(panel.w, panel.h) * 0.6" description="请上传图片"></el-empty>
</template>

<script>
export default {
  name: 'DashboardImage',
  inject: ['panel'],
  props: {
    title: {
      type: String,
      default: '',
    },
    dataset: {
      type: Object,
      default() {
        return {
          src: '',
          style: {
            width: '100%',
            height: '100%',
          },
        };
      },
    },
  },
};
</script>
