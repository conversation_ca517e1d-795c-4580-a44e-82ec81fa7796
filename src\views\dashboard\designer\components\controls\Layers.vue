<template>
  <sau-scrollbar class="designer-layers">
    <div class="designer-layers-filter">
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" size="mini" suffix-icon="el-icon-search" clearable></el-input>
    </div>
    <el-tree
      ref="tree"
      class="designer-layers-tree"
      node-key="id"
      default-expand-all
      draggable
      :data="list"
      :props="{ label: 'name' }"
      :filter-node-method="filterNode"
      :allow-drop="checkAllowDrop"
    >
      <span slot-scope="{ node, data }" class="tree-node">
        <div class="tools-left"></div>
        <div class="text">{{ node.label }}</div>
        <div class="tools-right">
          <div class="node-properties square" @click="changeToProperties(data)"><i class="el-icon-tickets"></i></div>
          <div class="node-visible square" @click="data.visible = !data.visible"><i v-show="data.visible" class="el-icon-view"></i></div>
        </div>
      </span>
    </el-tree>
  </sau-scrollbar>
</template>

<script>
export default {
  name: 'Layers',
  inject: ['context'],
  props: {
    current: {
      type: Array,
      default() {
        return [];
      },
    },
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      filterText: '',
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    current(val) {
      this.$refs.tree.setCheckedKeys(val.map((p) => p.id));
    },
  },
  methods: {
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      const name = data.name || '';
      return name.indexOf(value) !== -1;
    },
    checkAllowDrop(draggingNode, dropNode, type) {
      return type !== 'inner';
    },
    changeToProperties(panel) {
      this.context.setSelection(panel);
    },
  },
};
</script>
