export default {
  grid: {
    containLabel: true,
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  aria: {
    enabled: true,
  },
  tooltip: {
    trigger: 'item',
    axisPointer: {
      type: 'shadow',
    },
  },
  xAxis: {
    type: 'value',
    show: false,
    axisLine: {
      show: false,
    },
  },
  yAxis: [
    {
      type: 'category',
      show: false,
      inverse: true,
      axisLine: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    {
      type: 'category',
      show: true,
      position: 'right',
      inverse: true,
      axisLine: {
        show: false,
      },
      axisLabel: {
        formatter: '{value}',
      },
      axisTick: {
        show: false,
      },
    },
  ],
  series: [
    {
      type: 'bar',
      barWidth: '60%',
      label: {
        show: true,
        formatter: '{b}',
        // color: '#000',
        position: [0, -14],
        offset: [0, 0],
      },
      encode: {
        x: 'value',
        y: 'name',
      },
    },
    // {
    //   type: 'bar',
    //   yAxisIndex: 1,
    //   label: {
    //     show: false,
    //   },
    //   encode: {
    //     y: 'value',
    //   },
    // },
  ],
};
