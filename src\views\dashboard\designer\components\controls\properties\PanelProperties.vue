<template>
  <div class="designer-properties designer-properties-panel">
    <p class="panel-id">
      <el-tooltip class="item" effect="dark" :content="panel.editable ? '未锁定' : '已锁定'" placement="bottom">
        <el-link
          :type="panel.editable ? 'success' : 'info'"
          :icon="panel.editable ? 'el-icon-unlock' : 'el-icon-lock'"
          :underline="false"
          @click="panel.editable = !panel.editable"
        ></el-link>
      </el-tooltip>
      <span style="margin-left: 10px; vertical-align: middle; user-select: text">#{{ dashboard.custom.idPrefix + panel.id }}</span>
      <el-link :underline="false" style="float: right" @click="handleCustomDataset">···</el-link>
    </p>
    <el-form ref="form" :model="panel" label-width="80px" label-suffix=" :" size="mini">
      <el-form-item label="面板标题">
        <el-input v-model.trim="panel.name"></el-input>
      </el-form-item>

      <el-form-item label="面板位置" style="margin-bottom: 5px">
        <el-col :span="9">
          <el-input v-model.number="panel.x"></el-input>
        </el-col>
        <el-col class="line" :span="4">-</el-col>
        <el-col :span="9">
          <el-input v-model.number="panel.y"></el-input>
        </el-col>
      </el-form-item>

      <el-form-item v-if="dashboard.type == 1" label="网格大小" style="margin-bottom: 5px">
        <el-col :span="9">
          <el-input v-model.number="panel.w"></el-input>
        </el-col>
        <el-col class="line" :span="4">-</el-col>
        <el-col :span="9">
          <el-input v-model.number="panel.h"></el-input>
        </el-col>
      </el-form-item>

      <el-form-item label="面板大小" style="margin-bottom: 5px">
        <el-col :span="9">
          <el-input v-model.number="panelWidth"></el-input>
        </el-col>
        <el-col class="line" :span="4">-</el-col>
        <el-col :span="9">
          <el-input v-model.number="panelHeight"></el-input>
        </el-col>
      </el-form-item>

      <el-form-item style="margin-bottom: 2px; color: #8e8e8e">
        <el-col :span="9">
          {{ panelRatio[0] }}
        </el-col>
        <el-col class="line" :span="4">×</el-col>
        <el-col :span="9">
          {{ panelRatio[1] }}
        </el-col>
      </el-form-item>

      <el-form-item label="主题面板" style="margin-bottom: 5px">
        <el-switch v-model="panel.wrapped"></el-switch>
      </el-form-item>
      <component :is="panelProperties" ref="properties" :panel="panel" :dataset="panel.dataset" @panel="handlePost"></component>

      <div style="margin: 10px 0">
        <el-button type="success" size="mini" @click="showMyComponentsDialog">
          <i class="el-icon-upload2"></i>
          <slot>我的组件</slot>
        </el-button>
        <sau-dialog title="上传到我的组件" :visible.sync="myComponentsDialog.visible" :append-to-body="true" :force-middle="true" width="450px">
          <el-form ref="form" :model="myComponentsDialog.form" label-width="100px" label-suffix="：" size="mini">
            <el-form-item label="组件名称">
              <el-input v-model.lazy="myComponentsDialog.form.name"></el-input>
            </el-form-item>
            <el-form-item label="目标组件">
              <el-cascader
                ref="componentSelector"
                :options="myComponentsDialog.componentSelector.options"
                :props="myComponentsDialog.componentSelector.props"
                filterable
                clearable
              ></el-cascader>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="cancelUploadToMyComponents">取 消</el-button>
            <el-button type="primary" size="mini" @click="uploadToMyComponents">确 定</el-button>
          </span>
        </sau-dialog>
      </div>
    </el-form>
  </div>
</template>

<script>
import layout from '@/views/dashboard/player/core/layout';
import { integerRatio } from '@/utils/calculate';

import PanelPropertiesDefault from './PanelPropertiesDefault';
import { getComponent, myComponentsFolder, registerComponent } from '@/myComponents';
import { getRef } from '@/views/dashboard/player/core/refs';
import { addViewComponet, getViewComponetsPage, updateViewComponet } from '@/api/myComponents';
import { getPropertiesExtension } from '@/myComponents/extension';
import { extend } from '@/utils';

export default {
  name: 'PanelProperties',
  components: {
    PanelPropertiesDefault,
  },
  inject: ['context'],
  mixins: [layout],
  props: {
    designer: {
      type: Object,
      require: true,
    },
    dashboard: {
      type: Object,
      require: true,
    },
    panels: {
      type: Array,
      default() {
        return [];
      },
    },
    selection: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      myComponentsDialog: {
        visible: false,
        componentSelector: {
          props: {
            label: 'name',
            value: 'id',
          },
          options: [],
        },
        form: {
          name: '',
        },
      },
    };
  },
  computed: {
    // 选择的第一个面板
    panel() {
      return this.selection[0];
    },
    panelRatio() {
      return integerRatio(this.panelWidth, this.panelHeight);
    },
    panelWidth: {
      get: function () {
        return this.dashboard.type === 1 ? this.gridWidthToWidth(this.panel.w, this.dashboard) : this.panel.w;
      },
      set: function (v) {
        this.panel.w = this.dashboard.type === 1 ? this.widthToGridWidth(v, this.dashboard) : v;
      },
    },
    panelHeight: {
      get: function () {
        return this.dashboard.type === 1 ? this.gridHeightToHeight(this.panel.h, this.dashboard) : this.panel.h;
      },
      set: function (v) {
        this.panel.h = this.dashboard.type === 1 ? this.heightToGridHeight(v, this.dashboard) : v;
      },
    },
    panelProperties() {
      const panel = this.panel;
      const { type } = panel;
      const componentInfo = getComponent(type);
      const properties = (componentInfo && componentInfo.properties) || PanelPropertiesDefault;

      const propertiesExtension = getPropertiesExtension(type);
      const extensions = propertiesExtension.reduce((pre, handler) => {
        const extension = handler(panel);

        if (extension) {
          if (Array.isArray(extension)) {
            return extension.concat(pre);
          } else {
            pre.unshift(extension);
          }
        }

        return pre;
      }, []);

      if (extensions.length > 0) {
        return {
          props: {
            panel: Object,
            dataset: Object,
          },
          render() {
            return (
              <div>
                {extensions.map((extension) => (
                  <extension props={this.$props} />
                ))}
                <properties props={this.$props} on={this.$listeners}></properties>
              </div>
            );
          },
        };
      }

      return properties;
    },
  },

  // watch: {
  //   panel(value) {
  //     if (value) {
  //       this.$nextTick(() => {
  //         $('.sau-panel', this.$el).draggable({
  //           handle: '.sau-panel--title',
  //           distance: 10,
  //           drag: function (event, ui) {
  //             ui.helper.css({
  //               position: 'fixed',
  //               'z-index': 99999,
  //             });
  //             ui.position.left = ui.offset.left;
  //             ui.position.top = ui.offset.top;
  //           },
  //           // stop: function () {
  //           //   $(this)
  //           //     .resizable({
  //           //       minHeight: 50,
  //           //     })
  //           //     .resizable(this.style.position ? 'enable' : 'disable');
  //           // },
  //         });
  //
  //         $(this.$parent.$el).droppable({
  //           accept: '.sau-panel',
  //           classes: {
  //             'ui-droppable-active': 'designer-state-highlight',
  //           },
  //           drop: function (event, ui) {
  //             ui.helper.removeAttr('style');
  //           },
  //         });
  //       });
  //     }
  //   },
  // },

  mounted() {
    this.$nextTick(() => {
      const panel = getRef(this.panel);
      panel.$properties = this.$refs.properties;
    });
  },

  methods: {
    showMyComponentsDialog() {
      this.myComponentsDialog.componentSelector.options = myComponentsFolder.children;

      this.myComponentsDialog.form.name = this.panel.name;
      this.myComponentsDialog.visible = true;
    },
    uploadToMyComponents() {
      const { type, w, h, editable, wrapped, visible, dataset } = this.panel;

      const name = this.myComponentsDialog.form.name;
      const checkedNodes = this.$refs.componentSelector.getCheckedNodes();

      // 数据库参数
      const data = {
        name,
        type,
        width: w,
        height: h,
        enable: '1',
        desc: '',
        wrapped: wrapped ? '1' : '0',
        editable: editable ? '1' : '0',
        visible: visible ? '1' : '0',
        dataset,
      };

      //内存中的我的组件对象
      const myComponent = {
        name,
        folder: 'myComponents',
        type,
        width: w,
        height: h,
        enable: '1',
        desc: '',
        wrapped,
        editable,
        visible,
        dataset,
      };

      if (checkedNodes.length === 1) {
        data.id = checkedNodes[0].data.id;
        updateViewComponet(data).then(() => {
          extend(true, checkedNodes[0].data, myComponent);

          this.myComponentsDialog.visible = false;
          this.$message.success('成功更新到我的组件');
        });
      } else {
        addViewComponet(data).then(() => {
          registerComponent(myComponent);
          this.myComponentsDialog.visible = false;
          this.$message.success('成功保存到我的组件');
        });
      }
    },
    cancelUploadToMyComponents() {
      this.myComponentsDialog.visible = false;
      this.$message({
        type: 'info',
        message: '取消保存至我的组件',
      });
    },

    handlePost(type, ...args) {
      const panel = getRef(this.panel);
      panel[type].apply(this, args);
    },
    handleCustomDataset() {
      this.context.showDatasetDialog(this.panel);
    },
  },
};
</script>
