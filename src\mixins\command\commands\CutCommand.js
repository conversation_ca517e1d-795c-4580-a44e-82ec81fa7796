import Command from '../Command';

class CutCommand extends Command {
  name = '剪切';

  execute() {
    const selection = this.app.getSelection();
    this.app.clipboard.data = selection;
    this.app.clipboard.once = true;
    this.app.deleteSelection(selection);
    this.cache = selection;
    return true;
  }

  undo() {
    if (this.cache) {
      this.app.addSelection(this.cache);
    }
    return true;
  }

  redo() {
    if (this.cache) {
      this.app.deleteSelection(this.cache);
    }
    return true;
  }
}

export default CutCommand;
