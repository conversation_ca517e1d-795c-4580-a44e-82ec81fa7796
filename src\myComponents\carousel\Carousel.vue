<template>
  <el-carousel :autoplay="dataset.autoplay" :interval="dataset.interval" :indicator-position="dataset.indicatorPosition">
    <el-carousel-item v-for="(item, index) in dataset.items" :key="index">
      <DashboardView ref="views" :panel="item"></DashboardView>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
import DashboardView from '@/views/dashboard/player/DashboardView.vue';

export default {
  components: { DashboardView },
  props: {
    name: String,
    dataset: {
      type: Object,
      default() {
        return {
          items: [],
          autoplay: true,
          interval: 3000,
          indicatorPosition: '',
        };
      },
    },
  },
  computed: {
    animate: {
      get: function () {
        return this.dataset.autoplay;
      },
      set: function (v) {
        this.$refs.views.forEach((item) => (v ? item.startAnimate() : item.stopAnimate()));
      },
    },
  },
  methods: {
    load() {
      this.$refs.views.forEach((item) => {
        item.load();
      });
    },
  },
};
</script>
<style lang="stylus" scoped>
.el-carousel {
  height: 100%;
}

>>> .el-carousel__container, .el-carousel__item {
  height: 100%;
}
</style>
