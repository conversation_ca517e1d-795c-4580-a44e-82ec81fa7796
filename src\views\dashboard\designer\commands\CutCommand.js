import Command from '@/mixins/command/Command';
import { cloneDeep } from '@/utils';

class CutCommand extends Command {
  name = '剪切';

  execute() {
    const selection = cloneDeep(this.app.getSelection());
    this.app.clipboard.data = selection;
    this.app.clipboard.once = true;
    this.cache = selection;
    this.app.deleteContent(selection);
    return true;
  }

  undo() {
    if (this.cache) {
      this.app.addContent(this.cache);
    }
    return true;
  }

  redo() {
    if (this.cache) {
      this.app.deleteContent(this.cache);
    }
    return true;
  }
}

export default CutCommand;
