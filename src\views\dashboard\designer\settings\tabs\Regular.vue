<template>
  <div>
    <el-form ref="form" :model="dashboard" label-suffix="：" label-width="120px">
      <el-form-item label="看板名称" :rules="[{ required: true, message: '看板名称', trigger: 'blur' }]">
        <el-input v-model="dashboard.name"></el-input>
      </el-form-item>

      <el-form-item label="描述">
        <el-input v-model="dashboard.desc" type="textarea" :rows="5"></el-input>
      </el-form-item>

      <el-form-item label="类型" :rules="[{ required: true, message: '类型', trigger: 'blur' }]">
        <el-select v-model="dashboard.type" placeholder="请选择看板类型" :disabled="typeLock">
          <el-option v-for="(item, i) in dashboardType" :key="i" :label="item.label" :value="item.value"></el-option>
        </el-select>

        <span class="form-item-flowtip">
          <el-tooltip class="item" effect="dark" :content="typeLock ? '类型已锁定' : '类型未锁定'" placement="bottom">
            <el-link
              :type="typeLock ? 'success' : 'warning'"
              :icon="typeLock ? 'el-icon-lock' : 'el-icon-unlock'"
              :underline="false"
              @click="toggleTypeLock"
            ></el-link>
          </el-tooltip>
        </span>
      </el-form-item>
      <el-form-item label="可编辑" :rules="[{ required: true, message: '可编辑', trigger: 'blur' }]">
        <el-switch v-model="dashboard.editable"></el-switch>
      </el-form-item>

      <h3>时间选项</h3>

      <el-form-item label="时区">
        <el-select v-model="dashboard.timezone" placeholder="请选择时区">
          <el-option label="北京时间（GMT + 8）" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="自动刷新时间">
        <el-select v-model.number="dashboard.autoRefresh" filterable allow-create placeholder="请选择自动刷新时间">
          <el-option v-for="(item, i) in refreshInterval" :key="i" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <el-button type="success" size="small" icon="el-icon-s-promotion" @click="saveVBoard()">保 存</el-button>
    <el-button v-if="dashboard.id" size="small" icon="el-icon-document-copy" @click="saveVBoard(true)">另存为</el-button>
    <el-button v-if="dashboard.id" size="small" icon="el-icon-download" @click="exportVBoard(dashboard.id)">导出</el-button>
    <el-button type="danger" size="small" plain @click="rmVBoard(dashboard)">删 除</el-button>
  </div>
</template>

<script>
import { rmVBoard } from '@/api';
import { DASHBOARD_TYPE, REFRESH_INTERVAL } from '@/constant/designer';

export default {
  props: {
    dashboard: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  inject: ['context'],
  data() {
    return {
      dashboardType: DASHBOARD_TYPE,
      refreshInterval: REFRESH_INTERVAL,
      typeLock: true,
    };
  },
  methods: {
    toggleTypeLock() {
      if (this.typeLock) {
        this.$confirm('切换看板类型将重置全部面板, 是否继续?', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.typeLock = false;
        });
      } else {
        this.typeLock = true;
      }
    },
    exportVBoard(id) {
      window.open('/bh-business/modules/panel/export?id=' + id);
    },
    saveVBoard(isNew) {
      this.context.saveDashBoard(false, isNew);
    },
    rmVBoard({ id, name }) {
      if (id) {
        this.$confirm('此操作将永久删除该看板, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          rmVBoard(id).then(() => {
            this.$router.push('/designerx', () => {
              this.$message({
                message: '已删除 看板：' + name,
                type: 'success',
              });
            });
          });
        });
      } else {
        this.$message({
          message: '请先发布看板',
          type: 'warning',
        });
      }
    },
  },
};
</script>

<style scoped>
.form-item-flowtip {
  margin-left: 15px;
}
</style>
