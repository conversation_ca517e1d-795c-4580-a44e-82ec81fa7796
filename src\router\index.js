export function getDesignerRoutes() {
  return [
    {
      path: '/designerx/:id?',
      menuPath: '/designerx',
      name: 'designerx',
      component: () => import('@/views/dashboard/designer'),
      props: true,
      meta: {
        title: '看板设计器',
        icon: 'el-icon-edit-outline',
      },
    },
    {
      path: '/preview/:id?',
      menuPath: '/preview',
      name: 'preview',
      component: () => import('@/views/dashboard/preview'),
      props: true,
      hidden: true,
      meta: {
        title: '看板预览',
        icon: 'el-icon-view',
      },
    },
    {
      path: '/b/:id?',
      menuPath: '/b',
      name: 'dashboard',
      component: () => import('@/views/dashboard/player'),
      props: true,
      hidden: true,
      meta: {
        title: '看板',
        icon: 'el-icon-view',
      },
    },
    {
      path: '/v/:id?',
      menuPath: '/v',
      name: 'visualization',
      component: () => import('@/views/visualization/player'),
      props: true,
      hidden: true,
      meta: {
        title: '可视化',
        icon: 'el-icon-view',
      },
    },
    {
      path: '/plugins',
      menuPath: '/plugins',
      name: 'plugins',
      component: () => import('@/views/PluginManage'),
      props: true,
      hidden: true,
      meta: {
        title: '插件管理',
        icon: 'el-icon-view',
      },
    },
  ];
}
