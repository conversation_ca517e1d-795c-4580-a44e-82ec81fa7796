import { MessageBox } from 'element-ui';
import { compare } from 'compare-versions';

import { version } from '../../package.json';

function createVersionHtml(v) {
  return compare(version, v, '=') ? `<b>V${version}</b>` : `<b>V${v}</b><br/><span style="opacity: 0.5">V${version}</span>`;
}

export function setVersion(value = version, callback) {
  const VERSION = document.createElement('p');
  VERSION.className = 'dashboard-version';
  VERSION.innerHTML = createVersionHtml(value);
  VERSION.onclick = function () {
    MessageBox.prompt('', '请输入版本号', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputValue: value,
      inputPattern: /\d+\.\d+\.\d+(-\w+(\.\w+)*)?/,
      inputErrorMessage: '请输入正确的版本号',
    })
      .then(({ value }) => {
        VERSION.innerHTML = createVersionHtml(value);
        if (callback) callback(value);
      })
      .catch(() => {});
  };
  this.firstChild.append(VERSION);
}

export default version;
