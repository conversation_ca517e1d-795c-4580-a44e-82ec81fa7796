/**
 * @Description: 快捷键
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/10
 */
import Mousetrap from 'mousetrap'; // https://craig.is/killing/mice
import 'mousetrap/plugins/global-bind/mousetrap-global-bind'; // https://craig.is/killing/mice

export default {
  methods: {
    bindShortcuts(key, handler, { global } = {}) {
      if (global) {
        Mousetrap.bindGlobal(key, handler);
      } else {
        Mousetrap.bind(key, handler);
      }
    },
    unbindShortcuts() {
      Mousetrap.unbind.apply(this, arguments);
    },
  },
};
