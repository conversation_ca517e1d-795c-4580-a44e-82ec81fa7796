let maxPanelId = 0;

/**
 * 生成面板Id，保证不重复
 * @param id
 * @returns {number}
 */
function generatePanelId(id) {
  let pid;
  if (id) {
    pid = parseInt(id);
  } else {
    pid = ++maxPanelId;
  }
  if (maxPanelId < pid) {
    maxPanelId = pid;
  }
  return pid;
}

class Panel {
  id = '';
  name = ''; // 面板名称
  type = ''; // 面板类型
  desc = '';
  wrapped = true; // 主题面板
  editable = true; // 可编辑（移动，调整大小）
  visible = true; // 可见性
  dataset = {}; // 数据集

  createTime = '';
  updateTime = '';

  w = 300;
  h = 200;
  x = 0;
  y = 0;
  zIndex = 1024; // 层级

  constructor(id) {
    this.id = generatePanelId(id);
  }

  get i() {
    return this.id;
  }

  /**
   * 重新生成Id
   */
  regenerateId() {
    this.id = generatePanelId();
  }
}

export default Panel;
