.dashboard-layout {
  font-size: 18px;
  letter-spacing: 2px;
  white-space: nowrap;
  color: #fff;

  .sau-panel {
    height: 100%;
    min-width: auto;
    min-height: auto;
    border: 0px;

    .sau-panel--header {
      padding: 0;
      display: flex;
      height: 30px;
      line-height: 30px;
      border: 0px;
      color: #C6EEFF;
      font-size: 14px;
      font-style: italic;
      font-weight: bolder;
      letter-spacing: 2px;
      white-space: nowrap;
      overflow: hidden;
      background: none;

      > .sau-panel--title {
        height: 30px;
        line-height: 15px;
        padding: 5px 15px;
        border-bottom: 2px #35F7FF solid;
        overflow: visible;
        box-sizing: border-box;
      }

      &:after {
        content: '';
        margin-left: -2px;
        margin-top: 10px;
        display: block;
        width: 100%;
        height: 20px;
        background: rgba(18, 41, 56, 0.541);
        transform: skewX(-30deg) translateX(5px);
        border-left: 2px #35f7ff solid;
        border-top: 2px #35f7ff solid;
        border-image: linear-gradient(to right, #35f7ff, #0149ea) 10 20;
      }

      +.sau-panel--body {
        &:before {
          position: absolute;
          top: -1px;
          left: 0;
          content: '';
          width: 50px;
          display: block;
          height: 7px;
          background: #35f7ff;
        }

        &:after {
          position: absolute;
          top: -1px;
          left: 49px;
          content: '';
          width: 0;
          height: 0;
          border-left: 4px solid #35f7ff;
          border-bottom: 7px solid transparent;
        }
      }
    }

    .sau-panel--body {
      background: none;
      position: relative;
      background: #1229388a;
    }
  }

  .echarts {
    letter-spacing: normal;
  }
}
