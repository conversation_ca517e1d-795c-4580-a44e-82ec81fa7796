import DashboardProperties from './DashboardProperties';
import PanelProperties from './PanelProperties';

export default {
  name: 'Properties',
  props: {
    designer: {
      type: Object,
      require: true,
    },
    dashboard: {
      type: Object,
      require: true,
    },
    panels: {
      type: Array,
      default() {
        return [];
      },
    },
    selection: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  components: {
    DashboardProperties,
    PanelProperties,
  },
  render: function (createElement) {
    return createElement('sau-scrollbar', [
      createElement('keep-alive', [createElement(this.selection.length === 1 ? 'PanelProperties' : 'DashboardProperties', { props: this.$props })]),
    ]);
  },
};
