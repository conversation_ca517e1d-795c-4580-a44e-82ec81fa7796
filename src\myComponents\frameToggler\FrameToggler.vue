<template>
  <div class="dashboard-frameToggler">
    <template v-if="pages.length > 0">
      <div class="toggler-btns">
        <el-link
          v-for="(page, i) in pages"
          :key="i"
          :class="['toggler-btn', activePage == i ? 'active' : '']"
          :underline="false"
          @click="activePage = i"
        >
          {{ page.name }}
        </el-link>
      </div>
      <iframe name="bh-frame-0" width="100%" height="100%" frameborder="0" scrolling="no" :src="pages[activePage].src"></iframe>
    </template>
    <h1 v-else>暂无页面</h1>
  </div>
</template>

<script>
export default {
  name: 'FrameToggler',
  props: {
    dataset: {
      type: Object,
      default() {
        return {
          pages: [
            // {
            //   name: '主控',
            //   src: "/bhApp/findTopo/?tptName=%E6%8B%93%E6%89%91%E5%9B%BE2"
            // },
            // {
            //   name: '备控',
            //   src: "/bhApp/findTopo/?tptName=%E7%A2%A7%E6%85%A7%E7%BD%91%E7%BB%9C%E6%8B%93%E6%89%91%E5%9B%BE"
            // },
          ],
        };
      },
    },
  },
  data() {
    return {
      activePage: 0,
    };
  },
  computed: {
    pages() {
      return this.dataset ? this.dataset.pages : [];
    },
  },
};
</script>

<style scoped>
.dashboard-frameToggler {
  height: 100%;
}

.toggler-btns {
  position: absolute;
  top: 60px;
  left: 50px;
}

.toggler-btn {
  margin: 0 10px;
}

.toggler-btn.active {
  color: #fff;
}
</style>
