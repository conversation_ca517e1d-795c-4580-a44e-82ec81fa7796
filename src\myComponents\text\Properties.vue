<template>
  <div>
    <el-form-item label="文本内容" style="margin-top: 15px">
      <el-input v-model="dataset.text" clearable></el-input>
    </el-form-item>
    <el-form-item label="作为标题">
      <el-switch v-model="asTitle" @change="handleAsTitleChange"></el-switch>
    </el-form-item>
    <!--    <el-form-item label="自适应">-->
    <!--      <el-switch v-model="dataset.fit"></el-switch>-->
    <!--    </el-form-item>-->
    <sau-panel title="文字格式" collapsable virtual-scrollbar>
      <TextProperties :props="dataset.style" />
    </sau-panel>

    <SyncDatasetButton :panel="panel" :source="dataset" />
  </div>
</template>

<script>
import TextProperties from '@/views/dashboard/designer/components/controls/properties/TextProperties';

export default {
  components: {
    TextProperties,
  },
  props: {
    panel: Object,
    dataset: Object,
  },
  data() {
    return {
      asTitle: this.checkIsTitle(),
    };
  },
  methods: {
    checkIsTitle() {
      const regex = /(^|\s)title(\s|$)/;
      return regex.test(this.dataset.className);
    },

    handleAsTitleChange(value) {
      const { className } = this.dataset;
      if (value) {
        this.dataset.className += ' title';
      } else {
        const regex = /(^|\s)title(\s|$)/g;
        this.dataset.className = className.replace(regex, ' ').trim(); // 用空格替换"title"单词，并删除前后的空格
      }
    },
  },
};
</script>
