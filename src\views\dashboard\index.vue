<template>
  <div class="dashboard-centre">
    <el-input v-model="input" placeholder="请输入内容" clearable></el-input>
    <el-row :gutter="18">
      <el-col :sm="8" :md="6" :lg="4" :xl="3">
        <el-card class="dashboard-card dashboard-add" :body-style="{ padding: '0px' }">
          <router-link :to="{ name: 'designerx' }">
            <div class="add-icon">
              <i class="el-icon-circle-plus-outline"></i>
            </div>
            <div class="bottom-text">
              <el-link>新建看板</el-link>
            </div>
          </router-link>
          <div class="bottom-items">
            <div class="bottom-btn" style="border-right: 1px solid #ccc">
              <el-link @click="templateDialog.visible = true">模版中心</el-link>
            </div>
            <div class="bottom-btn">
              <el-upload action="" accept=".board" :show-file-list="false" :auto-upload="false" :on-change="(file) => importVBoard(file)">
                <el-link>导入</el-link>
              </el-upload>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col v-for="vBoard in vBoards.filter((data) => !input || data.name.includes(input))" :key="vBoard.id" :sm="8" :md="6" :lg="4" :xl="3">
        <el-card class="dashboard-card" :body-style="{ padding: '0px' }">
          <router-link :to="{ name: 'dashboard', params: { id: vBoard.id } }">
            <div class="dashboard-preview">
              <skeleton v-if="!vBoard.loaded" type="rect" :square="true" height="150px" />
              <img v-show="vBoard.loaded" :src="vBoard.src" alt="预览" @load="vBoard.loaded = true" @error="previewLoadError(vBoard)" />
            </div>
          </router-link>

          <div style="padding: 14px">
            <el-link @click="$router.push({ name: 'designerx', params: { id: vBoard.id } })">
              {{ vBoard.name }}
            </el-link>
            <div class="bottom clearfix">
              <time class="time">
                {{ vBoard.updateTime || ' - ' }}
              </time>

              <span class="operate-btn-g">
                <el-button type="danger" class="button" icon="el-icon-close" circle @click="rmVBoard(vBoard)"></el-button>
                <!--                <el-button-->
                <!--                  class="button"-->
                <!--                  icon="el-icon-share"-->
                <!--                  circle-->
                <!--                ></el-button>-->
              </span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <sau-dialog title="模版中心" :visible.sync="templateDialog.visible" width="90%">
      <el-row>
        <el-col v-for="item in templates" :key="item.name" :xs="24" :sm="12" :md="8" :lg="6" :xl="4" style="padding: 5px">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>{{ item.name }}</span>
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                @click="
                  $router.push({
                    name: 'designerx',
                    params: { dashboard: item.template },
                  })
                "
              >
                应用
              </el-button>
            </div>
            <div style="height: 128px; text-align: center">
              <el-image v-if="item.preview" :src="item.preview" fit="contain" :preview-src-list="[item.preview]" style="height: 128px"></el-image>
              <i v-else class="el-icon-picture-outline-round" style="font-size: 100px; line-height: 128px; color: #aaa"></i>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </sau-dialog>
  </div>
</template>

<script>
import Skeleton from '@/components/skeleton/Skeleton';

import { getVBoards, importVBoard, rmVBoard } from '@/api';
import templates from '../../preset/dashboard/templates';

import pic_preview_empty from '@/assets/images/designer/preview-empty.png';

export default {
  name: 'DashboardCentre',
  components: {
    Skeleton,
  },
  data() {
    return {
      input: '',
      vBoards: [],
      templates,
      templateDialog: {
        visible: false,
      },
    };
  },
  mounted() {
    this.load();
  },
  methods: {
    load() {
      return getVBoards().then(({ data }) => {
        this.vBoards = data.map((d) => {
          d.src = '/bh-business/modules/file/getImage?id=' + d.id;
          d.loaded = false;
          return d;
        });
      });
    },
    importVBoard(data) {
      const loading = this.$loading({
        lock: true,
        text: '正在解析数据...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      importVBoard({
        partial: '0',
        file: data.raw,
      })
        .validate(({ msg }) => {
          loading.close();
          return msg || '导入失败，请稍后再试';
        })
        .then(({ data }) => {
          loading.text = '解析数据完成，准备加载数据...';
          delete data.id; // 清除数据中的id
          this.$router.push({
            name: 'designerx',
            params: { dashboard: data },
          });
          setTimeout(() => {
            loading.close();
          }, 1000);
        });
    },
    rmVBoard(vBoard) {
      const { id, name } = vBoard;
      if (id) {
        this.$confirm('此操作将永久删除该看板, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          rmVBoard(id)
            .validate(`删除看板 ${name} `)
            .then(() => {
              const index = this.vBoards.indexOf(vBoard);
              this.vBoards.splice(index, 1);
              this.$message.success(`已删除看板 ${name}`);
            });
        });
      } else {
        this.$message({
          message: '请先发布看板',
          type: 'warning',
        });
      }
    },
    previewLoadError(vBoard) {
      vBoard.src = pic_preview_empty;
    },
  },
};
</script>
