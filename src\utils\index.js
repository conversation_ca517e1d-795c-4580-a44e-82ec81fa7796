export const cloneDeep = require('lodash.clonedeep');
export const debounce = require('lodash.debounce');

/**
 * 纯净克隆
 * @param source
 */
export function clonePure(source) {
  let target;
  if (source) {
    if (Array.isArray(source)) {
      target = source.map((item) => clonePure(item));
    } else if (typeof source === 'object') {
      target = Object.keys(source).reduce((pre, key) => ((pre[key] = clonePure(source[key])), pre), new source.constructor());
    } else {
      target = source;
    }
  } else {
    target = source;
  }
  return target;
}

export function downloadFile(content, filename) {
  const blob = new Blob([content]);
  // IE
  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    window.navigator.msSaveOrOpenBlob(blob, filename);
  } else {
    imatateDownloadByA(window.URL.createObjectURL(blob), filename);
  }
}

/**
 * 通过a标签模拟下载
 * @param {String} href
 * @param {String} filename
 */
export function imatateDownloadByA(href, filename) {
  const a = document.createElement('a');
  a.download = filename;
  a.style.display = 'none';
  a.href = href;
  document.body.appendChild(a);
  a.click();
  a.remove();
  window.URL.revokeObjectURL(href);
}

export function dataURLtoFile(dataurl, filename) {
  // 将base64转换为文件
  var arr = dataurl.split(',');
  var mime = arr[0].match(/:(.*?);/)[1];
  var bstr = atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

export function mergeObject(target, source) {
  if (typeof target !== 'object') {
    target = {};
  }
  if (Array.isArray(source)) {
    return source;
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (sourceProperty && typeof sourceProperty === 'object') {
      target[property] = mergeObject(target[property], sourceProperty);
    } else if (sourceProperty !== undefined && sourceProperty !== null) {
      target[property] = sourceProperty;
    }
  });
  return target;
}

// 定义一个工具方法，接收一个布尔值参数和若干个对象参数
export function extend() {
  // 获取参数列表
  var args = Array.prototype.slice.call(arguments);
  // 判断第一个参数是否为布尔值，如果是，则表示是否进行深度合并
  var deep = typeof args[0] === 'boolean' ? args.shift() : false;
  // 获取目标对象，即第一个参数
  var target = args[0];
  // 遍历剩余的源对象
  for (var i = 1; i < args.length; i++) {
    var source = args[i];
    // 遍历源对象的可枚举属性
    for (var key in source) {
      if (Object.prototype.hasOwnProperty.call(source, key)) {
        // 如果进行深度合并，并且源对象和目标对象的属性都是对象或数组，则递归调用extend方法
        if (
          deep &&
          (Object.prototype.toString.call(source[key]) === '[object Object]' || Object.prototype.toString.call(source[key]) === '[object Array]')
        ) {
          // 如果目标对象没有该属性，或者该属性不是对象或数组，则初始化为空对象或数组
          if (!target[key] || Object.prototype.toString.call(target[key]) !== Object.prototype.toString.call(source[key])) {
            target[key] = Object.prototype.toString.call(source[key]) === '[object Object]' ? {} : [];
          }
          // 递归调用extend方法，将源对象的属性合并到目标对象的属性中
          extend(deep, target[key], source[key]);
        } else {
          // 否则，直接复制源对象的属性值到目标对象中
          target[key] = source[key];
        }
      }
    }
  }
  // 返回修改后的目标对象
  return target;
}

/**
 * 根据属性拷贝源对象的值到目标对象
 * @param target
 * @param source
 * @returns {*}
 */
export function copyObjectByProperties(target, source) {
  if (source) {
    Object.keys(target).forEach(function (key) {
      if (source[key] !== undefined) {
        if (typeof target[key] === 'object') {
          copyObjectByProperties(target[key], source[key]);
        } else {
          target[key] = source[key];
        }
      }
    });
  }
  return target;
}

function fallbackCopyTextToClipboard(text) {
  var textArea = document.createElement('textarea');
  textArea.value = text;

  // Avoid scrolling to bottom
  textArea.style.top = '0';
  textArea.style.left = '0';
  textArea.style.position = 'fixed';

  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    var successful = document.execCommand('copy');
    var msg = successful ? 'successful' : 'unsuccessful';
    console.log('Fallback: Copying text command was ' + msg);
  } catch (err) {
    console.error('Fallback: Oops, unable to copy', err);
  }

  document.body.removeChild(textArea);
}

export function copyTextToClipboard(text) {
  if (!navigator.clipboard) {
    fallbackCopyTextToClipboard(text);
    return;
  }
  navigator.clipboard.writeText(text).then(
    function () {
      console.log('Async: Copying to clipboard was successful!');
    },
    function (err) {
      console.error('Async: Could not copy text: ', err);
    }
  );
}
