@font-face {
  font-family: 'pangmen';
  src: url('~@/assets/fonts/庞门正道标题体.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'FZXiaoBiaoSong-B05S';
  src: url('~@/assets/fonts/方正小标宋简体.TTF');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'FangSong_GB2312';
  src: url('~@/assets/fonts/仿宋_GB2312.ttf');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'KaiTi_GB2312';
  src: url('~@/assets/fonts/楷体_GB2312.ttf');
  font-weight: normal;
  font-style: normal;
}

.dashboard-loading {
  font-size: 50px;
  line-height: 50px;
}

.dashboard-player {
  position: relative;
  width: 100%;
  height: 100%;

  // 最大最小化
  .dashboard-layout {
    .vdr, .dashboard-item, .vue-grid-item {
      transition: all 0.3s !important;

      &.fill {
        top: 0px !important;
        left: 0px !important;
        width: 100% !important;
        height: 100% !important;
        transform: none !important;
        z-index: 1024;
      }
    }

    .vue-grid-item.fill {
      position: fixed !important;
    }
  }
}

.dashboard-layout {
  position: relative;
  background-position: center;

  &.custom {
    .dashboard-item {
      position: absolute;
      z-index: 100;
    }
  }

  .vue-grid-layout {
    position: relative;
    z-index: 1;
  }

  .particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

.dashboard-view, .dashboard-view>.sau-panel {
  height: 100%;
  min-width: auto;
  min-height: auto;
}

.dashboard-view-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999999;
  cursor: move;
}

.dashboard-color {
  color: inherit !important;
}
