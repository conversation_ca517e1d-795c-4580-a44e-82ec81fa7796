import Command from '@/mixins/command/Command';

class AddPanelCommand extends Command {
  name = '新增面板';

  data;

  execute() {
    this.cache = this.app.convertToPanel(this.data);
    this.app.addPanel(this.cache);
    return true;
  }

  undo() {
    if (this.cache) {
      this.app.deletePanel(this.cache);
    }
    return true;
  }

  redo() {
    if (this.cache) {
      this.app.addPanel(this.cache);
    }
    return true;
  }
}

export default AddPanelCommand;
