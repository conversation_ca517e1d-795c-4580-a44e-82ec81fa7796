<template>
  <div>
    <el-form-item label="数据源">
      <el-link type="info" :underline="false" @click="showDataSourceSelector">{{ caption }}</el-link>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'DataSourceProperties',
  inject: ['context'],
  props: {
    props: {
      type: Object,
      required: true,
      validator: function (value) {
        return value && Object.hasOwnProperty.call(value, 'enable');
      },
    },
  },
  computed: {
    caption() {
      return this.props.enable ? this.props.url.value || '未配置' : '未启用';
    },
  },
  methods: {
    showDataSourceSelector() {
      this.context.showDataSourceSelector(this.props);
    },
  },
};
</script>
