import $ from 'jquery';

import { version } from '../package.json';

import vendor from '@/vendor';

import Homepage from '@/views/homepage';
import Designer from '@/views/dashboard/designer';
import DashboardPlayer from '@/views/dashboard/player';

import DataSourceProperties from '@/views/dashboard/designer/components/controls/properties/DataSourceProperties';
import SyncDatasetButton from '@/views/dashboard/designer/components/SyncDatasetButton';

import { getDesignerRoutes } from '@/router';

import { getComponentList, registerFolder, registerComponent, registerMyComponents } from '@/myComponents';
import { registerPropertiesExtension } from '@/myComponents/extension';

import '@/styles/index.styl';
import { getViewPluginList } from '@/api/plug_in';
import Message from '@/utils/message';

const components = {
  DataSourceProperties,
  SyncDatasetButton,
};

const install = function (Vue) {
  if (install.installed) {
    return;
  }

  Vue.use(vendor['ElementUI']);
  Vue.use(vendor['SauUI']);

  Object.keys(components).forEach((key) => {
    Vue.component(key, components[key]);
  });
};

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);

  Object.keys(vendor).forEach((key) => {
    window[key] = vendor[key];
  });
}

const API = {
  version,
  install,
  components,
  views: {
    Homepage,
    Designer,
    DashboardPlayer,
  },
  routes: [
    {
      path: '/',
      component: Homepage,
      name: 'root',
    },
    {
      path: '/home',
      redirect: '/',
      name: 'home',
    },
  ].concat(getDesignerRoutes()),
  util: {
    registerFolder,
    registerComponent,
    getDesignerRoutes,
    getComponentList,

    // 注册属性过滤
    registerPropertiesExtension,
  },
  vendor,

  ready(callback) {
    getViewPluginList()
      .then(({ data = [] }) => {
        Promise.allSettled(
          data.map(({ url }) => {
            if (url) {
              $('head').append('<link href="' + url + '/dashboard.css" rel="stylesheet"></link>');

              const script = document.createElement('script');
              script.src = url + '/dashboard.umd.min.js';

              const promise = new Promise(function (resolve, reject) {
                script.onload = resolve;
                script.onerror = reject;
              });

              document.head.appendChild(script);

              return promise;
            }
          })
        ).then(() => {
          registerMyComponents();
          callback && callback();
        });
      })
      .catch(() => {
        Message.error('加载插件失败，请稍后再试');
      });
  },
};

window.dashboard = API;

export default API;
