import framework from 'sau-framework';

import axios from 'axios';
import qs from 'qs';
import Message from '@/utils/message';
import { HTTP_STATUS_CODES } from '@/constant/http';

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
});

// 请求拦截
service.interceptors.request.use(
  (config) => {
    // 加载动画
    //startLoading()
    if (config.method === 'post') {
      if (typeof config.data !== 'string') {
        config.data = qs.stringify(config.data, { indices: false });
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
service.interceptors.response.use(
  (response) => {
    if (typeof response.data == 'string' && response.data.indexOf('logout') > -1) {
      framework.router.push(`/login?redirect=${framework.router.fullPath}`);
      window.location.reload();
    }
    return response;
  },
  (error) => {
    if (error.response?.status) {
      const code = 'CODE_' + error.response.status;
      error.message = HTTP_STATUS_CODES[code];
    }
    Message({
      message: error.message,
      type: 'error',
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

// 导出 axios 实例
export default service;
