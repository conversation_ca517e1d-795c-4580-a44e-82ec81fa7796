<template>
  <component :is="panelProperties" ref="properties" :panel="panel" :dataset="panel.dataset" @panel="handlePost"></component>
</template>
<script>
import PanelProperties from '@/views/dashboard/designer/components/controls/properties/PanelProperties.vue';

export default {
  name: 'ItemProperties',
  extends: PanelProperties,
  computed: {
    panel() {
      return this.$attrs.panel;
    },
  },
};
</script>
