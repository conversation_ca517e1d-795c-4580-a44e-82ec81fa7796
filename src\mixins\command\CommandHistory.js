class CommandHistory {
  history = [];
  index = -1;

  current() {
    return this.history[this.index];
  }

  get(index = this.index) {
    return this.history[index];
  }

  size() {
    return this.history.length;
  }

  push(command) {
    if (this.hasNext()) {
      this.history.splice(this.index + 1);
    }

    if (this.size() > 20) {
      this.history.shift();
    }

    this.history.push(command);
    this.index++;
  }

  hasPrevious() {
    return this.size() > 0 && this.index >= 0;
  }

  previous() {
    return this.history[--this.index];
  }

  hasNext() {
    return this.size() > 0 && this.index + 1 < this.size();
  }

  next() {
    return this.history[++this.index];
  }
}

export default CommandHistory;
