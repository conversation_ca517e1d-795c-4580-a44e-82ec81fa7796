<template>
  <el-button type="primary" size="mini" :disabled="syncing" @click="syncDataset">
    <i :class="syncing ? 'el-icon-loading' : 'el-icon-sort'"></i>
    <slot>数据同步</slot>
  </el-button>
</template>

<script>
import { clonePure } from '@/utils';

export default {
  name: 'SyncDatasetButton',
  props: {
    panel: {
      type: Object,
      default() {
        return {
          dataset: {},
        };
      },
    },
    source: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      syncing: false,
    };
  },
  methods: {
    // 同步dataset
    syncDataset() {
      this.syncing = true;
      try {
        this.panel.dataset = clonePure(this.source);
        this.$emit('onSyncDataset', this.panel);
        this.$message({
          message: '数据同步成功',
          type: 'success',
        });
      } catch (e) {
        this.$message({
          message: '数据同步失败',
          type: 'warning',
        });
      }
      setTimeout(() => {
        this.syncing = false;
      }, 1000);
    },
  },
};
</script>
