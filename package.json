{"name": "bh-dashboard", "version": "2.0.2", "author": "sau-group", "description": "南京碧慧可视化", "main": "dashboard/dashboard.umd.min.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build-lib": "vue-cli-service build --target lib --formats umd-min --name dashboard src/dashboard.js", "analyzer": "cross-env NODE_ENV=production npm_config_report=true npm run build", "lint": "vue-cli-service lint"}, "dependencies": {"vue": "2.7.14"}, "devDependencies": {"@babel/eslint-parser": "7.16.5", "@vue/cli-plugin-babel": "4.5.19", "@vue/cli-plugin-eslint": "4.5.19", "@vue/cli-plugin-unit-jest": "4.5.19", "@vue/cli-service": "4.5.19", "axios": "^1.6.2", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "10.0.3", "compare-versions": "^6.0.0", "compression-webpack-plugin": "6.1.1", "core-js": "^3.26.1", "dom-to-image": "^2.6.0", "echarts": "^5.4.3", "element-ui": "^2.15.14", "eslint": "7.32.0", "eslint-config-prettier": "6.11.0", "eslint-plugin-prettier": "3.1.3", "eslint-plugin-vue": "6.2.2", "jquery-ui": "^1.13.2", "jsencrypt": "^3.3.2", "mockjs": "^1.1.0", "mousetrap": "^1.6.5", "nosleep.js": "^0.12.0", "ol": "^6.6.1", "particles.js": "^2.0.0", "prettier": "2.8.0", "prettier-plugin-stylus-supremacy": "^1.0.4", "qs": "^6.11.2", "resize-detector": "^0.3.0", "sau-design": "^0.1.0", "sau-framework": "^0.2.4", "sau-insert-css": "^2.0.3", "sau-ui": "^1.0.0", "script-loader": "0.7.2", "stylus": "^0.59.0", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^6.0.11", "svg.filter.js": "^2.0.2", "svg.js": "^2.7.1", "vue-echarts": "^6.6.8", "vue-router": "^3.6.5", "vue-template-compiler": "2.7.14", "vue-uuid": "^2.0.2"}, "files": ["dashboard"], "browserslist": ["> 0.5%", "last 2 versions", "Firefox ESR", "not dead"]}