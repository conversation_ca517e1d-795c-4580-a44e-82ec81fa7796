<template>
  <div class="dashboard-layout design-view" :class="dashboard.type == 1 ? 'flow' : 'custom'" :style="layoutStyle">
    <sau-scrollbar v-if="dashboard.type == 1" style="height: 100%">
      <grid-layout
        :layout.sync="panels"
        :col-num="dashboard.layout.colNum"
        :row-height="dashboard.layout.rowHeight"
        :is-draggable="dashboard.editable"
        :is-resizable="dashboard.editable"
        :is-mirrored="false"
        :vertical-compact="true"
        :use-css-transforms="true"
        :margin="[10, 10]"
      >
        <grid-item
          v-for="panel in panels"
          :id="idPrefix + panel.id"
          :key="panel.id"
          :x="panel.x"
          :y="panel.y"
          :w="panel.w"
          :h="panel.h"
          :i="panel.id"
          :static="!panel.editable"
        >
          <DashboardView ref="views" :panel="panel" :mask="isMoveMode" @mousedown.native="$emit('onSelectPanel', panel)"></DashboardView>
        </grid-item>
      </grid-layout>
    </sau-scrollbar>
    <template v-else>
      <absolute-item
        v-for="panel in panels"
        :id="idPrefix + panel.id"
        :key="panel.id"
        :snap="true"
        :panel.sync="panel"
        :editable="panel.editable"
        :scale-ratio="designer.scale / 100"
        :style="{ zIndex: panel.zIndex }"
        @refLineParams="getRefLineParams"
      >
        <DashboardView ref="views" :panel="panel" :mask="isMoveMode" @mousedown.native="$emit('onSelectPanel', panel)"></DashboardView>
      </absolute-item>

      <!--辅助线-->
      <span
        v-for="item in vLine"
        v-show="item.display"
        :key="'v' + item.id"
        class="ref-line v-line"
        :style="{
          left: item.position,
          top: item.origin,
          height: item.lineLength,
        }"
      />
      <span
        v-for="item in hLine"
        v-show="item.display"
        :key="'h' + item.id"
        class="ref-line h-line"
        :style="{
          top: item.position,
          left: item.origin,
          width: item.lineLength,
        }"
      />
      <!--辅助线END-->
    </template>
  </div>
</template>

<script>
import AbsoluteItem from './components/layout/AbsoluteItem';
import { DISIGNER_MODE } from '@/constant/designer';

import Layout from '../player/Layout';
import { getImage } from '@/api';

export default {
  name: 'DesignView',
  components: { AbsoluteItem },
  extends: Layout,
  props: {
    designer: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      vLine: [],
      hLine: [],
    };
  },
  computed: {
    isMoveMode() {
      return this.designer.mode === DISIGNER_MODE.MOVE;
    },
    layoutStyle() {
      const style = this.dashboard.appearance.style;

      const background = this.dashboard.appearance.background;
      style.backgroundColor = background.color;
      style.backgroundImage = 'url(' + getImage(background.image.url) + ')';
      style.backgroundSize = background.image.size;
      style.backgroundRepeat = background.image.repeat;

      const size = this.dashboard.size;
      style.width = size[0] + 'px';
      style.height = size[1] + 'px';

      return style;
    },
  },
  methods: {
    // 辅助线回调事件
    getRefLineParams(params) {
      const { vLine, hLine } = params;
      let id = 0;
      this.vLine = vLine.map((item) => {
        item['id'] = ++id;
        return item;
      });
      this.hLine = hLine.map((item) => {
        item['id'] = ++id;
        return item;
      });
    },
  },
};
</script>
