export default {
  methods: {
    widthToGridWidth(width, dashboard) {
      return Math.ceil((width * dashboard.layout.colNum) / dashboard.size[0]);
    },
    heightToGridHeight(height, dashboard) {
      return Math.ceil(height / dashboard.layout.rowHeight);
    },
    gridWidthToWidth(gridWidth, dashboard) {
      return Math.trunc(dashboard.size[0] * (gridWidth / dashboard.layout.colNum));
    },
    gridHeightToHeight(gridHeight, dashboard) {
      return gridHeight * dashboard.layout.rowHeight;
    },
  },
};
