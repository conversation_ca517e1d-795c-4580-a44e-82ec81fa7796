<template>
  <sau-dialog title="地图上传" :visible.sync="dialogVisible" width="400px" :force-middle="true" :append-to-body="true" :destroy-on-close="true">
    <el-upload
      drag
      action="/bh-business/modules/file/saveZipFile"
      :before-upload="beforeMapUpload"
      :on-success="handleMapUploadSuccess"
      :on-error="handleMapUploadError"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        将文件拖到此处，或
        <em>点击上传</em>
      </div>
      <!--                <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过500kb，小于30MB</div>-->
      <div slot="tip" class="el-upload__tip">上传要求：仅支持zip压缩包;</div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <!--            <el-button @click="hide" size="mini">取 消</el-button>-->
      <el-button type="primary" size="mini" @click="hide">确 定</el-button>
    </div>
  </sau-dialog>
</template>

<script>
export default {
  name: 'UploadMapDialog',
  data() {
    return {
      dialogVisible: false,
    };
  },
  methods: {
    show() {
      this.dialogVisible = true;
    },
    hide() {
      this.dialogVisible = false;
    },
    beforeMapUpload({ name }) {
      const isZIP = name.length > 4 && name.substr(name.lastIndexOf('.')) === '.zip';
      // const isZIP = file.type === "application/x-zip-compressed";
      // const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isZIP) {
        this.$message.warning('必须上传地图专用ZIP');
      }
      // if (!isLt2M) {
      //     this.$message.error('上传地图大小不能超过 2MB!');
      // }
      return isZIP;
    },
    handleMapUploadSuccess() {
      this.$message.success('地图上传成功');
    },
    handleMapUploadError() {
      this.$message.error('地图上传失败');
    },
  },
};
</script>

<style scoped></style>
