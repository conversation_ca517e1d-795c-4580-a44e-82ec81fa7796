<template>
  <div>
    <el-form-item label="字体">
      <el-select v-model="props.fontFamily" placeholder="请选择" filterable allow-create clearable>
        <el-option label="默认" value="Times, 'Times New Roman', Georgia, serif"></el-option>
        <el-option label="sansserif" value="Verdana, Arial, Helvetica, sans-serif"></el-option>
        <el-option label="monospace" value="'Lucida Console', Courier, monospace"></el-option>
        <el-option label="cursive" value="cursive, serif"></el-option>
        <el-option label="fantasy" value="fantasy, serif"></el-option>
        <el-option label="宋体" value="SimSun, STSong, serif"></el-option>
        <el-option label="仿宋GB2312" value="FangSong_GB2312, serif"></el-option>
        <el-option label="仿宋" value="FangSong, STFangsong, serif"></el-option>
        <el-option label="黑体" value="SimHei, STHeiti, serif"></el-option>
        <el-option label="方正小标宋简体" value="FZXiaoBiaoSong-B05S, serif"></el-option>
        <el-option label="楷体GB2312" value="KaiTi_GB2312, serif"></el-option>
        <el-option label="楷体" value="KaiTi, STKaiti, serif"></el-option>
        <el-option label="隶书" value="LiSu, Noto Sans Lisu Regular, serif"></el-option>
        <el-option label="幼圆" value="YouYuan, serif"></el-option>
        <el-option label="微软雅黑" value="Microsoft YaHei, serif"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="文字大小">
      <el-input-number v-model.lazy="fontSize" controls-position="right" :min="1"></el-input-number>
      px
    </el-form-item>
    <el-form-item label="行高">
      <el-input-number v-model.lazy="lineHeight" controls-position="right" :min="1"></el-input-number>
      px
    </el-form-item>
    <el-form-item label="文字颜色">
      <el-color-picker v-model="props.color"></el-color-picker>
    </el-form-item>
    <el-form-item label="文字加粗">
      <el-select v-model="props.fontWeight" placeholder="请选择" filterable allow-create>
        <el-option label="正常" value="normal"></el-option>
        <el-option label="加粗" value="bold"></el-option>
        <el-option label="特粗" value="bolder"></el-option>
        <el-option label="偏细" value="lighter"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="文字间距">
      <el-input-number v-model="letterSpacing" controls-position="right" :min="0"></el-input-number>
      px
    </el-form-item>
    <el-form-item label="文字对齐">
      <el-select v-model="props.textAlign" placeholder="请选择">
        <el-option label="左对齐" value="left"></el-option>
        <el-option label="居中" value="center"></el-option>
        <el-option label="右对齐" value="right"></el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: 'TextProperties',
  props: {
    props: {
      type: Object,
      default() {
        return {
          fontFamily: "Times, 'Times New Roman', Georgia, serif",
          fontSize: '14px',
          lineHeight: '14px',
          color: '#000',
          fontWeight: 'normal',
          letterSpacing: '0px',
          textAlign: 'left',
        };
      },
    },
  },
  computed: {
    fontSize: {
      get: function () {
        return parseFloat(this.props.fontSize);
      },
      set: function (value) {
        this.props.fontSize = value + 'px';
      },
    },
    lineHeight: {
      get: function () {
        return parseFloat(this.props.lineHeight);
      },
      set: function (value) {
        this.props.lineHeight = value + 'px';
      },
    },
    letterSpacing: {
      get: function () {
        return parseFloat(this.props.letterSpacing);
      },
      set: function (value) {
        this.props.letterSpacing = value + 'px';
      },
    },
  },
};
</script>
