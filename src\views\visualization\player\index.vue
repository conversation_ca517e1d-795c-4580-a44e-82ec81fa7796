<template>
  <div class="visualization">
    <el-carousel v-if="data.mode == 1" height="100%" indicator-position="none" :autoplay="true">
      <el-carousel-item v-for="d in data.dashboards" :key="d">
        <!--                <dashboard  :id="d"/>-->
        <iframe :src="'#/b/' + d" frameborder="0"></iframe>
      </el-carousel-item>
    </el-carousel>
    <div v-else class="splicing-screen">
      <!--            <dashboard v-for="d in data.dashboards" :key="d"  :id="d" :style="{ width: 100 / data.dashboards.length + '%' }"/>-->

      <iframe v-for="d in data.dashboards" :key="d" :src="'#/b/' + d" frameborder="0" :style="{ width: 100 / data.dashboards.length + '%' }"></iframe>
    </div>
  </div>
</template>

<script>
import { getVisualization } from '@/api';

// import Dashboard from '../../dashboard/player';

export default {
  name: 'Visualization',
  // components: { Dashboard },
  props: {
    id: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      data: {},
    };
  },
  mounted() {
    getVisualization({ id: this.id }).then(({ data }) => {
      if (data.length > 0) {
        this.data = data[0];
      }
    });
  },
};
</script>

<style scoped>
.visualization {
  height: 100%;
}

.splicing-screen {
  height: 100%;
}

>>> .dashboard-layout {
  position: relative;
  display: inline-block;
  height: 100%;
}

>>> .el-carousel {
  height: 100%;
}

iframe {
  display: inline-block;
  width: 100%;
  height: 100%;
}
</style>
