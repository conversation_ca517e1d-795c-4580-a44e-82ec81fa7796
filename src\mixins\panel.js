/**
 * @Description: 基础面板
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/10
 */
import { removeListener } from 'resize-detector';

export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    emptyMsg: '',
  },
  data() {
    return {
      animate: false,
    };
  },
  methods: {
    load() {
      // console.log(this);
    },
    restore(func) {
      if (!this.reloaded) {
        func();
      }
    },
  },
  destroyed() {
    this.$el.__resize_listeners__ = null;
    removeListener(this.$el);
  },
};
