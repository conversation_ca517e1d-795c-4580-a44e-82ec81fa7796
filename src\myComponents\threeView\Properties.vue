<template>
  <div>
    <el-form-item label="App地址">
      <el-input v-model.trim="app.src" clearable></el-input>
    </el-form-item>
    <!--    <el-form-item label="模块选择" style="margin-bottom: 5px;">-->
    <!--      <el-cascader-->
    <!--        :options="options"-->
    <!--        :props="{ checkStrictly: true }"-->
    <!--        clearable-->
    <!--      ></el-cascader>-->
    <!--    </el-form-item>-->
    <!--    <el-form-item label="层级导航" style="margin-bottom: 5px;">-->
    <!--      <el-switch v-model="app.layeredNavigation"></el-switch>-->
    <!--    </el-form-item>-->
    <el-form-item label="铭牌展示" style="margin-bottom: 5px">
      <el-switch v-model="app.nameplate"></el-switch>
    </el-form-item>
    <el-form-item label="自动旋转" style="margin-bottom: 5px">
      <el-switch v-model="app.autoRotate"></el-switch>
    </el-form-item>
    <el-form-item label="全屏展示" style="margin-bottom: 5px">
      <el-switch v-model="app.fullScreen"></el-switch>
    </el-form-item>
    <el-form-item label="视角">
      <el-select v-model="app.visualAngle" placeholder="请选择视角">
        <el-option label="全局视角" :value="0"></el-option>
        <el-option label="俯视图" :value="1"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="动态展示">
      <el-select v-model="inspection" placeholder="请选择动态展示方式">
        <el-option label="无" value=""></el-option>
        <el-option label="设备巡检" :value="1"></el-option>
        <el-option label="视点动画" :value="2"></el-option>
      </el-select>
    </el-form-item>
    <template v-if="inspection == '1'">
      <!--      <el-form-item label="设备巡检" style="margin-bottom: 5px;">-->
      <!--        <el-switch v-model="app.inspection"></el-switch>-->
      <!--      </el-form-item>-->
      <el-form-item label="巡检方式">
        <el-select v-model="app.inspectionType" placeholder="请选择巡检方式">
          <el-option label="自动" :value="1"></el-option>
          <el-option label="路径" :value="2"></el-option>
        </el-select>
      </el-form-item>

      <template v-if="app.inspectionType == '2'">
        <el-form-item label="巡检路径">
          <el-select v-model="app.pathId" placeholder="请选择路径" clearable>
            <el-option v-for="path in pathList" :key="path.id" :label="path.tdInspectionPathName" :value="path.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="绘制路径">
          <el-button type="text" icon="el-icon-video-play" :disabled="core.pathDrawing" @click="core.pathDrawing = true">开始</el-button>
          <el-button type="text" icon="el-icon-circle-check" :disabled="!core.pathDrawing" @click="core.pathDrawing = false">完成</el-button>
        </el-form-item>
      </template>
    </template>
    <template v-else-if="inspection == '2'">
      <el-form-item label="视点动画">
        <el-select v-model="app.viewpointAnimation" placeholder="请选择视点动画">
          <el-option label="无" value="0"></el-option>
          <el-option v-for="(name, i) in core.viewAnimates" :key="i" :label="name" :value="name"></el-option>
        </el-select>
      </el-form-item>
    </template>

    <sau-panel title="机房展示" collapsable>
      <el-form-item label="事件等级">
        <el-select v-model="app.eventLevel" multiple filterable allow-create clearable placeholder="请选择事件等级">
          <!--        【传空：一般，重要，紧急】-->
          <!--        【5：通知】【10：一般】【15：重要】【20：紧急】-->
          <!--        【30：重要】【40：重要，紧急】-->
          <!--        <el-option label="全部" value=""/>-->
          <el-option label="通知" value="5" />
          <el-option label="一般" value="10" />
          <el-option label="重要" value="15" />
          <el-option label="紧急" value="20" />
        </el-select>
      </el-form-item>
    </sau-panel>

    <SyncDatasetButton :panel="panel" :source="dataset" style="margin-bottom: 15px" @onSyncDataset="saveTdConfig" />

    <sau-panel title="场景信息" collapsable>
      <el-input v-model="filterText" placeholder="输入关键字进行过滤" size="mini" suffix-icon="el-icon-search" clearable></el-input>
      <el-tree ref="tree" :data="core.nodes" :props="defaultProps" node-key="id" :filter-node-method="filterNode">
        <span slot-scope="{ node, data }" class="custom-tree-node">
          <span>{{ node.label }}</span>
          <span class="node-locate">
            <el-link type="info" icon="el-icon-aim" :underline="false" @click="locateNode(node, data)"></el-link>
          </span>
        </span>
      </el-tree>
    </sau-panel>
  </div>
</template>

<script>
import { findTdInspectionPathList, saveTdConfig, saveTdInspectionPath } from '@/api';
import { getRef } from '@/views/dashboard/player/core/refs';

export default {
  props: {
    panel: {
      type: Object,
    },
    dataset: {
      type: Object,
    },
  },
  data() {
    return {
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'text',
      },
      core: {
        viewAnimates: [], //  视点列表
        pathDrawing: false,
        nodes: [], // 模型结构
      },
      pathList: [],
    };
  },
  computed: {
    app() {
      return this.dataset.app;
    },
    inspection: {
      get() {
        return this.app.inspectionType ? 1 : this.app.viewpointAnimation ? 2 : '';
      },
      set(v) {
        switch (v) {
          case 1:
            this.app.inspectionType = 1;
            this.app.viewpointAnimation = '';
            break;
          case 2:
            this.app.inspectionType = '';
            this.app.viewpointAnimation = '0';
            break;
          default:
            this.app.inspectionType = '';
            this.app.viewpointAnimation = '';
            break;
        }
      },
    },
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    'app.pid': {
      immediate: true,
      handler: function () {
        this.loadInspectionPathList();
      },
    },
    'core.pathDrawing': {
      handler: function (value) {
        this.$emit('panel', value ? 'startDrawPath' : 'stopDrawPath');
      },
    },
  },

  mounted() {
    this.loadInspectionPathList();
    const panel = getRef(this.panel);
    panel.$watch(
      'nodes',
      (nodes) => {
        this.core.nodes = nodes;
      },
      {
        immediate: true,
      }
    );
    panel.$watch(
      'viewAnimates',
      (viewAnimates) => {
        this.core.viewAnimates = viewAnimates;
      },
      {
        immediate: true,
      }
    );
  },
  methods: {
    locateNode(n, { id }) {
      this.$emit('panel', 'focusNode', id);
    },
    filterNode(value, data) {
      if (!value) {
        return true;
      }
      return data.text.indexOf(value) !== -1;
    },
    saveTdConfig({ dataset }) {
      const panel = getRef(this.panel);
      dataset.app.current = panel.current;

      const {
        pid, //  项目ID
        src, //  项目地址
        layeredNavigation, //  层级导航
        nameplate, //  铭牌展示
        autoRotate, //  自动旋转
        fullScreen, //  全屏
        visualAngle, //  视角
        inspectionType, //  巡检方式
        pathId, //  路径ID
        viewpointAnimation, //  视点动画
        eventLevel = '', // 事件筛选
      } = this.app;

      saveTdConfig({
        id: pid,
        // tdModuleSearch,
        nameplate: nameplate ? 1 : 0,
        autoRotate: autoRotate ? 1 : 0,
        inspectionType,
        pathId,
        fullScreen: fullScreen ? 1 : 0,
        layeredNavigation: layeredNavigation ? 1 : 0,
        visualAngle,
        viewpointAnimation,
        eventLevel: '' + eventLevel,
      });
    },

    addDrewPath(pathInfo) {
      const pid = this.app.pid;
      if (pid) {
        this.$prompt('请输入路径名称', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^\S+(\s+\S+)*$/,
          inputErrorMessage: '路径名称不正确',
          closeOnClickModal: false,
        })
          .then(({ value }) => {
            saveTdInspectionPath({
              tdConfigId: this.app.pid,
              tdInspectionPathName: value,
              tdInspectionPath: JSON.stringify(pathInfo),
            }).then(() => {
              this.loadInspectionPathList();
              this.$message.success('绘制路径成功');
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消绘制路径',
            });
          });
      }
    },
    loadInspectionPathList() {
      findTdInspectionPathList(this.app.pid).then(({ data }) => {
        this.pathList = data || [];
      });
    },
  },
};
</script>

<style scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.custom-tree-node .node-locate {
  display: none;
}

.custom-tree-node:hover .node-locate {
  display: inline-block;
}

.el-tree {
  background: transparent;
  color: #fff;
}

>>> .el-tree .el-tree-node__content:hover,
>>> .el-tree-node:focus > .el-tree-node__content {
  background-color: #5c6161;
}
</style>
