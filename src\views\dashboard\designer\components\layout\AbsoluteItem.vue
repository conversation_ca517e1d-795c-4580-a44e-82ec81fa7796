<template>
  <DDR
    :class-name-active="editable ? 'active' : ''"
    :x="panel.x"
    :y="panel.y"
    :h="panel.h"
    :w="panel.w"
    :z="panel.zIndex"
    :scale-ratio="scaleRatio"
    :snap="snap"
    :draggable="editable"
    :resizable="editable"
    :min-width="1"
    :min-height="1"
    v-on="$listeners"
    @resizing="onResizing"
    @dragging="onDragging"
  >
    <slot></slot>
  </DDR>
</template>

<script>
import design from 'sau-design';

const { DDR } = design;

export default {
  name: 'AbsoluteItem',
  components: { DDR },
  props: {
    // 元素对齐
    snap: {
      type: Boolean,
      default: false,
    },
    // 当调用对齐时，用来设置组件与组件之间的对齐距离，以像素为单位
    snapTolerance: {
      type: Number,
      default: 5,
      validator: function (val) {
        return typeof val === 'number';
      },
    },
    // 缩放比例
    scaleRatio: {
      type: Number,
      default: 1,
      validator: (val) => typeof val === 'number',
    },
    editable: {
      type: <PERSON>olean,
      default: true,
    },
    panel: {
      type: Object,
      required: true,
    },
  },
  methods: {
    onDragging(x, y) {
      this.panel.x = x;
      this.panel.y = y;
    },
    onResizing(x, y, w, h) {
      this.panel.x = x;
      this.panel.y = y;
      this.panel.w = w;
      this.panel.h = h;
    },
  },
};
</script>
