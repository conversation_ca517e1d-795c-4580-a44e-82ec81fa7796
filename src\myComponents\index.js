import { uuid } from 'vue-uuid';

import text from './text';
import image from './image';
import top from './top';

import line from './chart/line';
import pie from './chart/pie';
import radar from './chart/radar';

import miniTimer from './miniTimer';

import dashboardMap from './map';
import dashboardThreeView from './threeView';
import dashboard<PERSON>rameToggler from './frameToggler';
import dashboardCarousel from './carousel';

import { getViewComponetsPage } from '@/api/myComponents';
import Message from '@/utils/message';

const _session = new Map();
const _folderSession = new Map();

const userStore = [];

// 外部注册的库组件
export const libComponents = [];

/* TODO
 * 听取程亮的建议，在可视化项目未成熟前，默认组件 改为 自定义组件 且下移至未分类前，我的组件 改为 系统组件
 * 还有src/views/myComponents也需要同步
 */

export const defaultFolder = {
  id: 'default',
  name: '自定义组件', // 默认组件
  children: [
    { type: 'DashboardText', name: '文本文字', width: 200, height: 100, component: text.component, properties: text.properties },
    { type: 'DashboardImage', name: '图片', width: 500, height: 400, component: image.component, properties: image.properties, wrapped: false },
    {
      type: 'DashboardTop',
      name: 'Top排行',
      width: 400,
      height: 350,
      component: top.component,
      properties: top.properties,
      preview: top.preview,
      wrapped: true,
    },
    {
      id: 'defaultCharts',
      name: '图表组件',
      children: [
        {
          type: 'DashboardLine',
          name: '折线图/柱状图',
          width: 400,
          height: 350,
          component: line.component,
          properties: line.properties,
          preview: line.preview,
          wrapped: true,
        },
        {
          type: 'DashboardPie',
          name: '饼图',
          width: 400,
          height: 350,
          component: pie.component,
          properties: pie.properties,
          preview: pie.preview,
          wrapped: true,
        },
        {
          type: 'DashboardRadar',
          name: '雷达图',
          width: 400,
          height: 350,
          component: radar.component,
          properties: radar.properties,
          preview: radar.preview,
          wrapped: true,
        },
      ],
    },
    // {
    //   type: 'DashboardMap',
    //   name: '地图组件',
    //   width: 800,
    //   height: 400,
    //   component: dashboardMap.component,
    //   properties: dashboardMap.properties,
    //   wrapped: true,
    // },
    {
      type: 'DashboardMiniTimer',
      name: '时间组件',
      width: 500,
      height: 50,
      component: miniTimer.component,
      properties: miniTimer.properties,
      wrapped: false,
    },
    {
      type: 'DashboardThreeview',
      name: '3D组件',
      width: 800,
      height: 400,
      component: dashboardThreeView.component,
      properties: dashboardThreeView.properties,
      wrapped: true,
    },
    {
      type: 'DashboardFrameToggler',
      name: '页面组',
      width: 800,
      height: 400,
      component: dashboardFrameToggler.component,
      properties: dashboardFrameToggler.properties,
      wrapped: false,
    },
    {
      type: 'DashboardCarousel',
      name: '轮播组件',
      width: 800,
      height: 400,
      component: dashboardCarousel.component,
      properties: dashboardCarousel.properties,
      wrapped: true,
    },
  ],
};

export const myComponentsFolder = {
  id: 'myComponents',
  name: '系统组件', // 我的组件
  children: [],
};

const uncategorizedFolder = {
  id: 'uncategorized',
  name: '未分类',
  children: [],
};

/**
 * 获取组件列表
 */
export function getComponentList() {
  return [myComponentsFolder, ...userStore, defaultFolder, uncategorizedFolder];
}

/**
 * 刷新缓存
 * @param source 指定源，未指定为整个组件仓库
 * @returns {boolean} 刷新结果
 */
export function refreshSession(source = getComponentList()) {
  try {
    if (Array.isArray(source)) {
      source.forEach((item) => {
        if (item.type && typeof item.component === 'object') {
          _session.set(item.type, item);
        } else if (item.children) {
          _folderSession.set(item.id, item);
          refreshSession(item.children);
        }
      });
    }
  } catch (e) {
    return false;
  }
  return true;
}

/**
 * 清除缓存
 */
export function clearSession() {
  _session.clear();
  _folderSession.clear();
}

/**
 * 获取所有组件信息
 */
export function getComponents() {
  return _session.values();
}

/**
 * 获取对应类型的组件
 * @param type
 */
export function getComponent(type) {
  return _session.get(type);
}

export function deleteComponent(type) {
  return _session.delete(type);
}

export function registerFolder({ id, name, folder }) {
  if (id) {
    const parent = _folderSession.get(folder);
    const item = {
      id,
      name,
      children: [],
    };

    if (parent) {
      parent.children.push(item);
    } else {
      userStore.push(item);
    }

    _folderSession.set(id, item);
  } else {
    throw new Error('Dashboard folder must has an id.');
  }
}

/**
 * 向系统注册组件
 * @param component 组件信息
 * @param isInternal 是否是内部的，默认不是
 */
export function registerComponent(
  { id = uuid.v1(), folder, type, component, properties, name, wrapped = true, preview, width, height, dataset },
  isInternal = false
) {
  if (type) {
    const registeredComponent = getComponent(type);

    // 如果type存在，component不存在，尝试去已注册的组件中寻找component
    if (!component) {
      if (registeredComponent) {
        component = registeredComponent.component;
        properties = registeredComponent.properties;
      } else {
        throw new Error('Dashboard found no available component.');
      }
    } else if (registeredComponent) {
      console.warn(`Dashboard component ${type} already exists.`);
    }

    const parent = _folderSession.get(folder) || uncategorizedFolder;
    const item = { id, type, component, name, properties, preview, width, height, wrapped, dataset };

    parent.children.push(item);

    if (!isInternal) {
      libComponents.push(item);
    }

    _session.set(type, item);
  } else {
    throw new Error('Dashboard component type is required.');
  }
}

// 初始化缓存
refreshSession();

export function registerMyComponents() {
  getViewComponetsPage()
    .validate('加载我的组件')
    .then(({ data }) => {
      data.forEach(({ id, type, component, properties, name, wrapped, editable, visible, width = 300, height = 300, dataset }) => {
        try {
          registerComponent(
            {
              id,
              folder: 'myComponents',
              name,
              type,
              width,
              height,
              wrapped: wrapped === '1',
              editable: editable === '1',
              visible: visible === '1',
              dataset,
            },
            true
          );
        } catch (e) {
          console.error(e);
          Message.error(`${name}组件加载失败`);
        }
      });
    });
}
