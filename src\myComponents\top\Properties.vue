<template>
  <div>
    <sau-panel title="数据源" collapsable virtual-scrollbar>
      <DataSourceProperties :props="dataset.dataSource"></DataSourceProperties>
    </sau-panel>

    <sau-panel title="筛选" collapsable virtual-scrollbar>
      <el-form-item label="数量">
        <el-input-number v-model.number="dataset.count" :min="1" :step-strictly="true"></el-input-number>
      </el-form-item>
      <template v-if="dataset.sort">
        <el-form-item label="排序">
          <el-switch v-model="dataset.sort.enable"></el-switch>
        </el-form-item>
        <el-form-item v-if="dataset.sort.enable">
          <el-select v-model="dataset.sort.orderBy" placeholder="请选择">
            <el-option label="降序" value="desc"></el-option>
            <el-option label="升序" value="asc"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <!--      <el-form-item label="横向">-->
      <!--        <el-switch v-model="isHorizontal"></el-switch>-->
      <!--      </el-form-item>-->
    </sau-panel>

    <SyncDatasetButton :panel="panel" :source="dataset" @onSyncDataset="onSyncDataset" />
  </div>
</template>

<script>
export default {
  inject: ['context'],
  props: {
    panel: Object,
    dataset: Object,
  },
  computed: {
    isHorizontal: {
      set: function (value) {
        if (value) {
          this.dataset.option.xAxis.type = 'value';
          this.dataset.option.yAxis.type = 'category';
          this.dataset.option.yAxis.inverse = true;
          this.dataset.option.series[0].label.rotate = 0;
          this.dataset.option.series[0].label.position = [0, -14];
        } else {
          this.dataset.option.xAxis.type = 'category';
          this.dataset.option.yAxis.type = 'value';
          this.dataset.option.yAxis.inverse = false;
          this.dataset.option.series[0].label.rotate = 90;
          this.dataset.option.series[0].label.position = [-16, '100%'];
        }
      },
      get: function () {
        return this.dataset.option.xAxis.type === 'value';
      },
    },
  },
  methods: {
    onSyncDataset() {
      this.$emit('panel', 'load');
    },
  },
};
</script>
