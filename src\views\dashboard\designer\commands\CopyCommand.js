import Command from '@/mixins/command/Command';

import Panel from '@/models/Panel';
import { cloneDeep } from '@/utils';

class CopyCommand extends Command {
  name = '复制';

  execute() {
    const selection = cloneDeep(this.app.getSelection());
    selection.forEach((item) => {
      if (item instanceof Panel) {
        item.id = null;
      }
    });
    this.app.clipboard.data = selection;
    this.app.clipboard.once = false;
    return false;
  }
}

export default CopyCommand;
