// 看板类型
export const DASHBOARD_TYPE = [
  {
    label: '首页',
    value: 1,
    desc: '网格布局，面板位置互斥',
  },
  {
    label: '大屏',
    value: 0,
    desc: '自由布局，面板位置可随意固定',
  },
];

export const DISIGNER_MODE = {
  HANDLE: 0,
  MOVE: 1,
};

export const SCOPE = {
  NONE: 0,
  OUTER: 1,
  INNER: 2,
  ALL: 3,
};

export const FIT_MODE = [
  { name: '原始', icon: '1.png', value: 0 },
  { name: '比例', icon: '2.png', resize: SCOPE.ALL, value: 1 },
  { name: '全比例', icon: '3.png', resize: SCOPE.OUTER, transform: SCOPE.INNER, value: 2 },
  // {name: '填充', icon: '4.png',value:3},
  { name: '适应', icon: '5.png', transform: SCOPE.OUTER, value: 4 },
  // {name: '居中', icon: '6.png',value:5},
];

export const COLOR_LIST = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];

// 预置自动刷新时间
export const REFRESH_INTERVAL = [
  { label: '不刷新', value: 0 },
  // {label: "每秒", value: 1000},
  { label: '每分', value: 60000 },
  { label: '每十分钟', value: 600000 },
  { label: '每十五分钟', value: 900000 },
  { label: '每三十分钟', value: 1800000 },
  { label: '每小时', value: 3600000 },
];

// 默认背景图
export const BACKGROUND_IMAGE = [
  {
    label: '默认一',
    src: require('@/assets/images/background/bg1.jpg'),
  },
  {
    label: '默认二',
    src: require('@/assets/images/background/bg2.jpg'),
  },
  {
    label: '默认三',
    src: require('@/assets/images/background/bg3.jpg'),
  },
  {
    label: '默认四',
    src: require('@/assets/images/background/bg4.jpg'),
  },
  {
    label: '默认五',
    src: require('@/assets/images/background/bg5.jpg'),
  },
  {
    label: '默认六',
    src: require('@/assets/images/background/bg6.jpg'),
  },
  // {
  //     label: "小兵哥",
  //     src: 'http://b.zol-img.com.cn/desk/bizhi/image/5/960x600/1406773868478.jpg'
  // },
];
