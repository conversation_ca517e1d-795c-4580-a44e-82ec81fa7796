<template>
  <div class="visualization-centre">
    <div class="project-title">
      <h2>全部可视化</h2>

      <el-form :inline="true" :model="formInline" size="mini" class="demo-form-inline" style="position: absolute; right: 0px; top: 15px">
        <el-form-item>
          <el-dropdown split-button type="primary" trigger="click" @click="edit()" @command="add">
            <i class="el-icon-edit"></i>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="true">新建目录</el-dropdown-item>
              <el-dropdown-item divided>新建可视化</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
        <el-form-item v-show="multipleSelection.length > 0">
          <el-button type="danger" plain icon="el-icon-delete" @click="deleteItem"></el-button>
        </el-form-item>
        <el-form-item class="v-search">
          <el-input v-model="input" suffix-icon="el-icon-search" placeholder="请输入内容" style="display: inline" clearable></el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-divider style="margin-top: 0px"></el-divider>
    <el-table
      ref="visualzationTable"
      :data="filterData"
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
      border
      stripe
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @select-all="selectAllRow"
      @selection-change="handleSelectionChange"
    >
      <!--      @select="selectRow"-->
      <el-table-column type="selection" width="40"></el-table-column>

      <el-table-column prop="name" label="名称" width="250" sortable>
        <template slot-scope="scope">
          <span v-if="checkIsVisualization(scope.row)">
            <router-link :to="{ name: 'visualization', params: { id: scope.row.id } }">
              <el-link>
                <i class="el-icon-postcard"></i>
                {{ scope.row.name }}
              </el-link>
            </router-link>
          </span>
          <span v-else>
            <i class="el-icon-folder"></i>
            {{ scope.row.name }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="desc" label="描述" />
      <el-table-column prop="type" label="类型" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.mode == 0" type="primary" disable-transitions>拼接</el-tag>
          <el-tag v-else-if="scope.row.mode == 1" type="success" disable-transitions>轮播</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="最后更新时间" sortable width="180"></el-table-column>
    </el-table>
    <VisualizationDialog ref="dialog" :is-folder="isFolder" :item="vItem" @submited="dialogSubmited"></VisualizationDialog>
  </div>
</template>

<script>
import VisualizationDialog from './dialogs/VisualizationDialog';

import { getVisualizaions, removeVisualizaionFolders, removeVisualizationNodes } from '@/api';

export default {
  name: 'VisualizationCentre',
  components: { VisualizationDialog },
  data() {
    return {
      input: '',
      tableData: [],
      formInline: {},
      isFolder: false,
      vItem: {},
      multipleSelection: [],
    };
  },
  computed: {
    filterData() {
      return this.input ? this.filterTree() : this.tableData;
    },
  },
  mounted() {
    this.load();
  },
  methods: {
    load() {
      getVisualizaions().then(({ data }) => {
        this.tableData = data;
      });
    },
    filterTree(tree = this.tableData, filterData = []) {
      for (let i = 0; i < tree.length; i++) {
        const leaf = Object.assign({}, tree[i], { children: [] });
        const children = [];
        if (tree[i].children?.length > 0) {
          this.filterTree(tree[i].children, children);
          leaf.children = children;
        }
        if (leaf.name.includes(this.input) || children.length > 0) {
          filterData.push(leaf);
        }
      }
      return filterData;
    },
    checkIsVisualization(item) {
      return Object.prototype.hasOwnProperty.call(item, 'mode');
    },
    selectAllRow(selection) {
      const store = this.$refs.visualzationTable.store;
      const isAllSelected = store.states.isAllSelected;
      if (!isAllSelected) {
        this.$refs.visualzationTable.clearSelection();
      } else {
        selection.forEach((row) => this.selectRow(true, row));
      }
    },
    selectRow(selection, row) {
      if (Array.isArray(row.children)) {
        row.children.forEach((r) => {
          this.$refs.visualzationTable.toggleRowSelection(r, selection);
          return this.selectRow(selection, r);
        });
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    add(isFolder) {
      if (this.multipleSelection?.length > 0) {
        const parentId = this.multipleSelection[0].folder || this.multipleSelection[0].id;
        this.vItem = {
          parent: parentId,
          folder: parentId,
        };
      } else {
        this.vItem = {};
      }
      this.isFolder = isFolder;
      this.$nextTick(() => {
        this.$refs.dialog.show();
      });
    },
    edit(isFolder) {
      if (this.multipleSelection?.length > 0) {
        this.vItem = this.multipleSelection[0];
      } else {
        this.vItem = {};
      }
      this.isFolder = isFolder;
      this.$nextTick(() => {
        this.$refs.dialog.show();
      });
    },
    dialogSubmited() {
      this.load();
    },
    async deleteItem() {
      const folders = [];
      const visualizations = [];
      this.multipleSelection.forEach((item) => {
        const id = item.id;
        if (this.checkIsVisualization(item)) {
          visualizations.push(id);
        } else {
          folders.push(id);
        }
      });
      if (folders.length > 0) {
        await removeVisualizaionFolders(folders);
      }
      if (visualizations.length > 0) {
        await removeVisualizationNodes(visualizations);
      }
      await this.load();
    },
  },
};
</script>

<style lang="stylus" scoped>
.visualization-centre {
  padding: 10px;
  background: #fff;
}

.project-title {
  position: relative;
  display: flex;
  align-items: center;
  padding: 5px 0;

  h2 {
    max-width: 200px;
    font-size: 14px;
    color: #2681FF;
    padding: 0 10px;
    border-left: 2px solid #2681FF;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .v-search {
    >>> .el-form-item__content {
      .el-input__suffix, .el-input__icon {
        line-height: 18px;
      }
    }
  }
}
</style>
