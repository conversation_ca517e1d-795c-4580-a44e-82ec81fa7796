import Vue from 'vue';
import framework from 'sau-framework';

import dashboard from '@/dashboard';

import './styles/index.styl';

import './user/login';
import './router';

import logo from './assets/logo.png';
import loginBg from './assets/bg.jpg';
const title = '数据可视化平台';

Vue.use(dashboard);

framework.customSetting({
  title,
  login: {
    formTitle: title,
    formLabel: '请登录',
    backgroundImage: loginBg,
    copyright: '©2020 南京碧慧',
  },
  logo: {
    label: title,
    image: logo,
  },
});

framework.init();
framework.render();
