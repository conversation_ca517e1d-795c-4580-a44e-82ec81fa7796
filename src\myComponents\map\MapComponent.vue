<template>
  <div class="dashboard-map">
    <slot></slot>
    <template v-if="core.markerSearch">
      <div class="map-query">
        <el-input v-model="query" size="small" placeholder="请输入内容" suffix-icon="el-icon-search" clearable @focus="onFocusQuery">
          <!--                <template slot="prepend">位置</template>-->
        </el-input>
      </div>
      <transition name="el-zoom-in-top">
        <div v-show="showQueryList" class="map-list">
          <el-table
            :data="core.markers.filter((data) => !query || data.name.toLowerCase().includes(query.toLowerCase()))"
            :show-header="false"
            height="300"
            style="width: 100%"
          >
            <el-table-column prop="name" label="名称" sortable>
              <template slot-scope="scope">
                <el-popover trigger="hover" placement="right">
                  <p>地址: {{ scope.row.position }}</p>
                  <p>描述: {{ scope.row.desc }}</p>
                  <div slot="reference" class="name-wrapper">
                    <el-button type="text" @click="navToMarker(scope.row)">
                      {{ scope.row.name }}
                    </el-button>
                  </div>
                </el-popover>
              </template>
            </el-table-column>
          </el-table>
          <el-button class="close-btn" icon="el-icon-close" circle @click="showQueryList = false"></el-button>
        </div>
      </transition>
    </template>
    <slot name="tools">
      <div class="map-tools">
        <el-tooltip v-if="core.tools.marker" class="item" effect="dark" content="自由移动" placement="bottom">
          <el-button icon="el-icon-thumb" size="mini" circle plain @click="onMarkerCommand"></el-button>
        </el-tooltip>
        <el-dropdown v-if="core.tools.marker" @command="onMarkerCommand">
          <el-button icon="el-icon-place" size="mini" circle plain @click="onMarkerCommand('add')"></el-button>
          <el-dropdown-menu slot="dropdown">
            <!--                    <el-dropdown-item icon="el-icon-thumb" command="pan">自由移动</el-dropdown-item>-->
            <el-dropdown-item icon="el-icon-add-location" command="add">增加标记</el-dropdown-item>
            <el-dropdown-item icon="el-icon-map-location" command="modify">修改标记</el-dropdown-item>
            <el-dropdown-item icon="el-icon-delete-location" command="delete">删除标记</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tooltip v-if="core.tools.upload" class="item" effect="dark" content="上传地图" placement="bottom">
          <el-button icon="el-icon-upload" size="mini" circle plain @click="uploadMap"></el-button>
        </el-tooltip>
      </div>
    </slot>
    <div v-show="selectedFeatures" ref="popup" class="overlay-markerInfo">
      <template v-if="selectedFeatures">
        <slot name="markerInfoOverlay">
          <p class="name">{{ selectedFeatures.name }}</p>
          <p>{{ selectedFeatures.position }}</p>
          <p>{{ selectedFeatures.desc }}</p>
        </slot>
      </template>
    </div>
    <MarkerInfoDialog ref="markerInfoDialog" @onSubmit="onEditMarker" @onCancel="onCancelEditMarker" />
    <UploadMapDialog ref="uploadMapDialog" />
  </div>
</template>

<script>
import { uuid } from 'vue-uuid';
import { addListener, removeListener } from 'resize-detector';
import debounce from 'lodash/debounce';

import { Map, View } from 'ol';
import Feature from 'ol/Feature';
import Point from 'ol/geom/Point';
import { fromLonLat, transform } from 'ol/proj';
import { defaults, Draw, Modify, Select } from 'ol/interaction';
import { click } from 'ol/events/condition';
import { getCenter } from 'ol/extent';
import { Icon, Style, Fill, Text } from 'ol/style';
import Overlay from 'ol/Overlay';

import { createBaseLayer, createDebgLayer, createMarkerLayer } from './layers/index';
import point from '@/assets/images/map/icon/point.png';

import MarkerInfoDialog from './dialogs/MarkerInfoDialog';
import UploadMapDialog from './dialogs/UploadMapDialog';

const ICON_STYLE = new Style({
  image: new Icon({
    anchor: [0.5, 25],
    anchorXUnits: 'fraction',
    anchorYUnits: 'pixels',
    src: point,
  }),
});

const COORD_BEIJING = fromLonLat(
  [116.391266, 39.907359] // 北京天安门wgs84经纬度
);

export default {
  name: 'DashboardMap',
  components: { MarkerInfoDialog, UploadMapDialog },
  props: {
    title: String,
    w: Number,
    h: Number,
    dataset: {
      type: Object,
      default() {
        return {
          tools: {
            marker: true,
            upload: true,
          },
          markerSearch: true,
        };
      },
    },
  },
  data() {
    const {
      center = COORD_BEIJING,
      zoom = 4,
      markers = [],
      tools = {
        marker: true,
        upload: true,
      },
      markerSearch = true,
    } = this.dataset;
    return {
      core: {
        center,
        zoom,
        markers,
        tools,
        markerSearch,
        zoomable: true,
        copyright: true,
      },
      query: '', // 搜索框
      showQueryList: false, // 显示搜索列表
      selectedFeatures: false,

      mode: '',
    };
  },
  mounted() {
    this.baseLayer = createBaseLayer();
    this.debgLayer = createDebgLayer();
    this.markerLayer = createMarkerLayer();

    const features = [];
    this.core.markers.forEach((marker) => {
      features.push(this.setPointer(new Feature(new Point(marker.coordinates)), marker));
    });
    this.markerLayer.getSource().addFeatures(features);

    this.view = new View({
      center: this.core.center,
      zoom: this.core.zoom,
      minZoom: 3,
    });

    this.map = new Map({
      // 图层
      layers: [
        this.baseLayer,
        // this.debgLayer,
        this.markerLayer,
      ],
      target: this.$el, //  目标容器
      // 视图
      view: this.view,
      controls: [],
      interactions: defaults({
        doubleClickZoom: false,
        // mouseWheelZoom: false,
        // shiftDragZoom: false,
      }),
    });

    this.popup = new Overlay({
      element: this.$refs.popup,
    });
    this.map.addOverlay(this.popup);

    this.map.on('pointermove', (evt) => {
      if (evt.dragging) {
        return;
      }
      this.markerLayer.getFeatures(evt.pixel).then((clickedFeatures) => {
        if (clickedFeatures.length) {
          this.map.getTarget().style.cursor = 'pointer';
        } else {
          this.map.getTarget().style.cursor = '';
        }
      });
    });

    this.map.on('click', (evt) => {
      if (evt.dragging) {
        return;
      }
      this.markerLayer.getFeatures(evt.pixel).then((features) => {
        if (features.length) {
          const marker = this.getMarkerById(features[0].getId());
          this.popup.setPosition(marker.coordinates);
          this.selectedFeatures = marker;
        } else {
          this.selectedFeatures = null;
        }
      });
    });

    this.map.on('dblclick', (evt) => {
      if (evt.dragging) {
        return;
      }
      this.markerLayer.getFeatures(evt.pixel).then((features) => {
        if (features.length) {
          const point = features[0];
          this._sessionFeature = point;
          const marker = this.getMarkerById(point.getId());

          if (this.mode == 'modify') {
            this.$refs.markerInfoDialog.show(marker);
          } else {
            this.showMarkerInfo(marker);
          }
        }
      });
    });

    this.updatePanel();
    this.syncDataset();
    this.onSizeChange();
  },
  destroyed() {
    if (this.__resizeHandler) {
      removeListener(this.$el, this.__resizeHandler);
    }
  },
  methods: {
    updatePanel() {
      this.$emit('updatePanel', {
        syncDataset: () => {
          // this.getCenter = Math.round(this.view.getCenter())

          // var mapExtent = map.getView().calculateExtent(map.getSize());
          // var point = ol.extent.getCenter(mapExtent)
          // point = transform([point[0], point[1]], 'EPSG:3857', 'EPSG:4326'),
          const mapExtent = this.map.getView().calculateExtent(this.map.getSize());
          const map_center = getCenter(mapExtent);

          this.core.zoom = Math.round(this.view.getZoom());
          this.core.center = map_center;

          // this.markerLayer.getSource().getFeatures().forEach(f => {
          //     console.log(f, f.getGeometryName(), this.getEPSG4326(f.getGeometry().getCoordinates()))
          // })

          this.syncDataset();
        },
      });
    },
    // 同步dataset
    syncDataset() {
      this.$emit('updateDataset', this.core);
    },

    // 坐标转换
    getEPSG4326(coordinates) {
      return transform(coordinates, 'EPSG:3857', 'EPSG:4326');
    },

    navToMarker({ coordinates }) {
      this.view.animate({
        center: coordinates,
        duration: 1000,
      });
    },

    //START 查询
    // 根据Id获取标记数据
    getMarkerById(id) {
      return this.core.markers.find((m) => m.id === id);
    },

    // 根据Id获取标记点
    getPointById(id) {
      return this.markerLayer.getSource().getFeatureById(id);
    },
    //END 查询

    //START 删除
    // 删除标记点
    deletePointer(pointer = this._sessionFeature) {
      if (pointer) {
        this.markerLayer.getSource().removeFeature(pointer);
        this.removeMarkerById(pointer.getId());
        this._sessionFeature = null;
      } else {
        this.$message.error('未获得标记信息');
      }
    },

    // 删除数据中的标记
    removeMarkerById(markerId) {
      const markers = this.dataset.markers;
      for (let i = 0; i < markers.length; i++) {
        let { id } = markers[i];
        if (id === markerId) {
          markers.splice(i, 1);
          break;
        }
      }
    },
    //END 删除

    onEditMarker(markerInfo) {
      if (!markerInfo.id) {
        markerInfo.coordinates = this._sessionFeature.getGeometry().getCoordinates();
        this.core.markers.push(markerInfo);
      }
      this.setPointer(this._sessionFeature, markerInfo);
      this._sessionFeature = null;
      this.syncDataset();
    },
    onCancelEditMarker(markerInfo) {
      // 没有标点id为新增，取消则删除添加上的标点
      if (!markerInfo.id) {
        this.deletePointer();
      }
    },

    // 设置标记点（应该说是格式化标记点），同步Id，设置样式
    setPointer(pointer, marker) {
      if (pointer) {
        const { id, name, textColor = '#fff', textBackground = '#ED7D31' } = marker;
        const iconStyle = new Style({
          image: new Icon({
            anchor: [0.5, 24],
            anchorXUnits: 'fraction',
            anchorYUnits: 'pixels',
            src: point,
          }),

          text: new Text({
            offsetY: -32,
            font: '12px bolder',
            padding: [5, 10, 5, 10],
            text: name,
            // stroke: new Stroke({
            //     color: '#FFF',
            //     width: 2
            // }),
            fill: new Fill({
              color: textColor,
            }),
            backgroundFill: new Fill({
              color: textBackground,
            }),
            textAlign: 'center',
            textBaseline: 'bottom',
          }),
        });
        if (!id) {
          marker.id = uuid.v1();
        }
        pointer.setId(marker.id); // 保持feature id和marker同步
        pointer.setStyle(iconStyle);
      }
      return pointer;
    },

    uploadMap() {
      this.$refs.uploadMapDialog.show();
    },

    //START 操作模式
    enterAddPointerMode() {
      this.interaction = new Draw({
        type: 'Point',
        source: this.markerLayer.getSource(),
        style: ICON_STYLE,
      });
      this.map.addInteraction(this.interaction);
      this.interaction.on('drawstart', (event) => {
        this.$refs.markerInfoDialog.show();
        this._sessionFeature = event.feature; // 将新添加的点放入内存中
      });
    },
    enterModifyPointerMode() {
      this.interaction = new Modify({
        hitDetection: this.markerLayer,
        source: this.markerLayer.getSource(),
        // features: new Collection(this.markerLayer.getSource().getFeatures())
      });
      this.map.addInteraction(this.interaction);
      this.interaction.on(['modifyend'], (event) => {
        if (event.features.getLength() > 0) {
          const selected = event.features.item(0);
          const marker = this.getMarkerById(selected.getId());
          marker.coordinates = selected.getGeometry().getCoordinates();
          this.popup.setPosition(null);
        }
      });
    },
    enterDeletePointerMode() {
      this.interaction = new Select({
        condition: click,
        layers: [this.markerLayer],
      });
      this.map.addInteraction(this.interaction);

      this.interaction.on('select', (event) => {
        const selected = event.selected;
        if (selected.length > 0) {
          this.$confirm('此操作将删除该标记, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => {
              this.deletePointer(selected[0]);
              this.popup.setPosition(null);
              this.$message({
                type: 'success',
                message: '删除成功!',
              });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除',
              });
            });
        }
      });
    },

    onMarkerCommand(command) {
      this.map.removeInteraction(this.interaction);
      this.mode = command;
      switch (command) {
        case 'add':
          this.enterAddPointerMode();
          break;
        case 'modify':
          this.enterModifyPointerMode();
          break;
        case 'delete':
          this.enterDeletePointerMode();
          break;
      }
    },
    //END 操作模式

    showMarkerInfo() {},

    onSizeChange() {
      this.__resizeHandler = debounce(
        () => {
          this.map.updateSize();
        },
        100,
        { leading: true }
      );
      addListener(this.$el, this.__resizeHandler);
    },
    onFocusQuery() {
      this.showQueryList = true;
    },
  },
};
</script>

<style lang="stylus">
@import '~ol/ol.css';

query_top = 20px;
query_left = 20px;
query_width = 250px;
query_height = 32px;

.dashboard-map {
  height: 100%;
  box-sizing: border-box;

  .overlay-markerInfo {
    margin-top: -10px;
    margin-left: 10px;
    padding: 4px 10px;
    line-height: 16px;
    background: #FFF;
    max-width: 300px;
    max-height: 150px;
    overflow: auto;

    .name {
      font-size: 12px;
      font-weight: 700;
      word-wrap: break-word;
      overflow: hidden;
      display: block;
    }

    > p {
      margin: 2px;
    }

    &:after {
      content: ' ';
      border-width: 5px;
      position: absolute;
      display: block;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      top: -5px;
      left: 0;
      border-right-color: #fff;
    }
  }

  .map-query {
    position: absolute;
    top: query_top;
    left: query_left;
    width: query_width;
    height: query_height;
    z-index: 9999;
  }

  .map-list {
    position: absolute;
    top: query_top + query_height;
    left: query_left;
    width: query_width;
    border: 1px solid #DCDFE6;
    box-sizing: border-box;
    background: #fff;
    z-index: 9999;

    .close-btn {
      float: right;
      margin: 5px;
      padding: 2px;
      font-size: 12px;
    }
  }

  .map-tools {
    position: absolute;
    top: query_top;
    right: query_left;
    z-index: 9999;

    > * {
      margin: 0 2.5px;
    }
  }
}
</style>
