<template>
  <div>
    <template v-for="(item, i) in list">
      <el-submenu v-if="Array.isArray(item.children)" :key="i" :index="indexPrefix + i">
        <template slot="title">
          <span>{{ item.name }}</span>
        </template>
        <ComponentsSubList
          v-if="item.children.length > 0"
          :index-prefix="indexPrefix + i + '-'"
          :list="item.children"
          v-on="$listeners"
        ></ComponentsSubList>
        <p v-else style="text-align: center; color: #ccc">暂无组件</p>
      </el-submenu>
      <el-menu-item v-else :key="i" :index="indexPrefix + i" :data="item">
        <ComponentsItem>
          <span>{{ item.name }}</span>
          <el-tooltip v-if="item.preview" placement="left-start">
            <div slot="content"><img :src="item.preview" width="200" alt="预览图" /></div>
            <span>
              <i class="el-icon-picture" style="font-size: 14px"></i>
            </span>
          </el-tooltip>
          <template v-slot:icons>
            <el-link :underline="false" @click="addPanel(item)">
              <i class="el-icon-circle-plus-outline" style="color: #0ba7d7"></i>
            </el-link>
          </template>
        </ComponentsItem>
      </el-menu-item>
    </template>
  </div>
</template>

<script>
import ComponentsItem from './ComponentsItem';

export default {
  name: 'ComponentsSubList',
  components: { ComponentsItem },
  inject: ['addPanel'],
  props: {
    indexPrefix: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      activeNames: [0],
    };
  },
};
</script>
