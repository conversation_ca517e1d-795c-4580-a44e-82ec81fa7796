<template>
  <div>
    <el-form-item label="背景颜色">
      <el-color-picker v-model="props.color" show-alpha :predefine="predefineColors"></el-color-picker>
    </el-form-item>

    <el-form-item label="背景图片">
      <el-upload
        ref="bgImageUploader"
        action="#"
        list-type="picture-card"
        :auto-upload="false"
        :limit="1"
        :file-list="fileList"
        :on-change="changeBgImage"
        :class="{ uploaded: backgroundAvailable }"
      >
        <i slot="default" class="el-icon-plus"></i>
        <div slot="file" slot-scope="{ file }">
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <span class="el-upload-list__item-actions">
            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
              <i class="el-icon-zoom-in"></i>
            </span>
            <span class="el-upload-list__item-delete" @click="handleDownload(file)">
              <i class="el-icon-download"></i>
            </span>
            <span class="el-upload-list__item-delete" @click="handleRemoveBgImg(file)">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </el-upload>
    </el-form-item>

    <el-form-item v-if="defaultBackgroundImages.length > 0" label="默认背景">
      <el-select :value="defaultBackgroundImageValue" clearable @change="(url) => (props.image.url = url)">
        <el-option v-for="img in defaultBackgroundImages" :key="img.src" :label="img.label" :value="img.src"></el-option>
      </el-select>
    </el-form-item>

    <template v-if="backgroundAvailable">
      <el-form-item label="背景大小">
        <el-select v-model="props.image.size" placeholder="请选择背景大小">
          <el-option label="默认" value="auto"></el-option>
          <el-option label="填充" value="cover"></el-option>
          <el-option label="适应" value="contain"></el-option>
          <el-option label="拉伸" value="100% 100%"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="重复方式">
        <el-select v-model="props.image.repeat" placeholder="请选择重复方式">
          <el-option label="重复" value="repeat"></el-option>
          <el-option label="不重复" value="no-repeat"></el-option>
        </el-select>
      </el-form-item>
    </template>
    <sau-dialog title="预览图" :visible.sync="dialogVisible" :force-middle="true" :append-to-body="true">
      <img class="overview-img" :src="dialogImageUrl" alt="" style="width: 100%" />
    </sau-dialog>
  </div>
</template>

<script>
import { imatateDownloadByA } from '@/utils';

import { getImage } from '@/api';

export default {
  name: 'BackgroundProperties',
  props: {
    props: {
      type: Object,
      default() {
        return {
          color: '',
          image: {
            repeat: 'no-repeat',
            size: '100%',
            url: '',
          },
          dynamic: '0', // 动态背景
        };
      },
    },
    defaultBackgroundImages: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',

      predefineColors: [
        '#000000', // 黑
        // '#ffffff',  // 白
        '#2e2e2e', // 黑灰
        '#8a919a', // 灰

        '#e70c0c',
        '#c7158577',

        '#ff4500',
        'rgba(255, 69, 0, 0.68)',

        'rgb(255, 120, 0)',
        '#ff8c00',
        'hsv(51, 100, 98)',
        '#ffd700',

        '#90ee90',
        'hsva(120, 40, 94, 0.5)',

        '#00ced1',

        '#3d30ba',
        '#0a5096',
        '#4282e2',
        '#1e90ff',
        'hsla(209, 100%, 56%, 0.73)',

        '#c71585',
      ],
    };
  },
  computed: {
    backgroundAvailable() {
      return Boolean(this.props.image.url);
    },
    fileList() {
      const url = this.props.image.url;
      return this.backgroundAvailable ? [{ name: typeof url == 'object' ? url.name : '背景图片', url: getImage(url) }] : [];
    },
    defaultBackgroundImageValue() {
      const url = this.props.image.url;
      return url && this.defaultBackgroundImages.find((item) => item.src === url) ? url : '';
    },
  },
  methods: {
    changeBgImage(img) {
      this.props.image.url = img;
    },

    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleDownload({ name = '背景图片.png', url }) {
      imatateDownloadByA(url, name);
    },
    handleRemoveBgImg() {
      this.$refs.bgImageUploader.clearFiles();
      this.changeBgImage('');
    },
  },
};
</script>
