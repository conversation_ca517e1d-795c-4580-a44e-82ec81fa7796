<template>
  <div class="nav-main">
    <canvas ref="canvas"></canvas>
    <span v-for="(item, i) in items" ref="items" :key="i" class="nav-span" :class="{ active: currentIndex == i }" @click="_toggleNav(i)">
      <slot :index="i" :item="item" :current="currentIndex" />
    </span>
  </div>
</template>

<script>
const j = 0.85;
const k = 30;
const l = 30;

export default {
  props: {
    speed: {
      type: Number,
      default: 0.01,
    },
    active: {
      type: Number,
      default: 0,
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      rendered: false,
      detectCount: 0,
      width: 0,
      height: 0,
      currentIndex: this.active,
    };
  },
  mounted() {
    setTimeout(() => {
      window.addEventListener('resize', this.resize);
      this.canvas = this.$refs.canvas;
      if (this.canvas) {
        this._detect();
      }
    }, 0);
  },
  destroyed() {
    window.removeEventListener('resize', this.resize);
    this.timer && cancelAnimationFrame(this.timer);
  },
  methods: {
    _detect() {
      this.detectTimer && clearTimeout(this.detectTimer);
      if (5 > this.detectCount) {
        if (!this.$refs.items?.length) {
          this.detectCount++;
          this.detectTimer = setTimeout(() => {
            this._detect();
          }, 100);
        } else {
          setTimeout(() => {
            this._calcTabs();
            if (!this.rendered) {
              this._initCanvas(this.canvas, this.width, this.height);
              this._createPattern(this.canvas);
              this.rendered = true;
              // this.currentIndex = 1;
              this.draw(0);
            }
          }, 1e3);
        }
      }
    },
    _initCanvas(canvas, width, height) {
      const d = window.devicePixelRatio;
      const context = canvas.getContext('2d');
      canvas.width = width * d;
      canvas.height = height * d;
      canvas.style.width = width + 'px';
      canvas.style.height = height + 'px';
      context.scale(d, d);
    },
    _toggleNav(cIndex) {
      if ('undefined' !== typeof cIndex && cIndex !== this.currentIndex && this.tabWidthList && this.tabWidthList.length) {
        if (!this.animating || cIndex !== this.nextIndex) {
          this.animating = true;
          this.distance = this.tabWidthList[cIndex] - this.tabWidthList[this.currentIndex];
          this.avgSpeed = this.calcAVGSpeed(this.distance);
          this.curDisX = 0;
          this.nextIndex = cIndex;
          this.$emit('update:active', cIndex);
        }
      }
    },
    _calcTabs() {
      if (this.$refs.items?.length) {
        const tabWidthList = [];
        let tabsWidth = 0;
        let tabsHeight = 0;
        this.$refs.items.forEach((i) => {
          tabWidthList.push(tabsWidth);
          tabsWidth += i.offsetWidth;
          if (i.offsetHeight > tabsHeight) {
            tabsHeight = i.offsetHeight;
          }
        });
        tabWidthList[0] = -20;
        tabWidthList.push(tabsWidth);
        this.tabWidthList = tabWidthList;
        this.tabsWidth = tabsWidth;
        this.tabsHeight = tabsHeight;
        this.width = this.$el.offsetWidth;
        // this.width = window.innerWidth
        this.height = this.tabsHeight + 20;
      }
    },
    draw(hlOffset) {
      this._drawHightlight(hlOffset);
      this.timer = requestAnimationFrame(() => {
        this.draw((hlOffset + this.speed) % 1.6);
      });
    },
    _drawHightlight(hlOffset) {
      const context = this.canvas.getContext('2d');
      const width = 0.3;

      context.clearRect(0, 0, 2 * this.width, 2 * this.height);
      context.shadowColor = 'rgba(36, 131, 255, 1)';
      context.shadowBlur = 5;
      context.strokeStyle = '#004CB3';
      context.lineWidth = 0.8;
      context.fillStyle = 'none';

      this._draw(context, false);

      const gradient = context.createLinearGradient(0, 0, this.width, this.height);
      const p = hlOffset - width;
      gradient.addColorStop(Math.min(1, Math.max(0, p)), 'rgba(0,0,0,0)');
      gradient.addColorStop(Math.min(1, Math.max(0, p + 0.1)), '#8ED6FF');
      gradient.addColorStop(Math.min(1, p + width), '#8ED6FF');
      gradient.addColorStop(Math.min(1, p + width + 0.1), 'rgba(0,0,0,0)');
      gradient.addColorStop(1, 'rgba(0,0,0,0)');
      context.lineWidth = 1.5;

      context.strokeStyle = gradient;

      context.fillStyle = this.pattern;
      this._draw(context, true);
    },
    _draw(context, fill) {
      const currentIndex = this.currentIndex;
      const tabHeight = this.tabsHeight;

      let f = 0;
      let g = 40;
      let i = 20;
      let j = 0.5;
      let k = 2.5;
      let l = 0;

      context.beginPath();
      context.moveTo(-50, this.height + 10);
      context.lineTo(-50, tabHeight + j);
      if (this.animating) {
        const m = this.getCurSpeed(this.curDisX, this.distance);
        l = Math.min(Math.abs(this.distance), Math.abs(this.curDisX + m)) * Math.sign(m);
      }
      context.lineTo(f + this.tabWidthList[currentIndex] + l - g / 2, tabHeight + j);
      this._calCurve(
        f + this.tabWidthList[currentIndex] + l - g / 2,
        tabHeight + j,
        f + this.tabWidthList[currentIndex] + l + g / 2,
        k + j,
        context,
        i
      );
      if (this.animating) {
        let o = this.tabWidthList[this.nextIndex + 1] - this.tabWidthList[this.nextIndex];
        context.lineTo(f + this.tabWidthList[currentIndex] + o + l - g / 2, k + j);
        this._calCurve(
          f + this.tabWidthList[currentIndex] + o + l - g / 2,
          k + j,
          f + this.tabWidthList[currentIndex] + o + l + g / 2,
          tabHeight + j,
          context,
          i
        );
      } else {
        context.lineTo(f + this.tabWidthList[currentIndex + 1] + l - g / 2, k + j);
        this._calCurve(
          f + this.tabWidthList[currentIndex + 1] + l - g / 2,
          k + j,
          f + this.tabWidthList[currentIndex + 1] + l + g / 2,
          tabHeight + j,
          context,
          i
        );
      }
      context.lineTo(this.width + 10, tabHeight + j);
      context.lineTo(this.width + 10, this.height + 10);
      context.closePath();
      context.stroke();

      if (fill) {
        context.fill();
      }
      if (this.animating && fill) {
        this.curDisX = l;
        if (Math.abs(l) >= Math.abs(this.distance)) {
          this.animating = false;
          this.currentIndex = this.nextIndex;
        }
      }
    },
    _createPattern(a) {
      let b = 140;
      let c = 63;
      let d = 1;
      let e = document.createElement('canvas');
      e.width = b;
      e.height = c;
      e.style.width = b / d + 'px';
      e.style.height = c / d + 'px';
      const context = e.getContext('2d');
      context.scale(d, d);
      context.lineWidth = 0.4;
      for (let g = 3, h = 0.8, j = 1; 30 > j; j++) {
        context.strokeStyle = 'RGBA(22, 120, 160, ' + h + ')';
        context.beginPath();
        context.moveTo(0, j * g);
        context.lineTo(b, j * g);
        context.stroke();
        context.closePath();
        if (10 < j) {
          h -= 0.1;
        }
      }
      this.pattern = a.getContext('2d').createPattern(e, 'repeat-x');
      e = null;
    },
    _calCurve(a, b, x, y, e, f) {
      e.bezierCurveTo(a + f, b, x - f, y, x, y);
    },
    calcAVGSpeed(a) {
      let b = (l * j * a + k * (1 - j) * a) / (k * l * 20);
      b = Math.max(Math.abs(b), 2.5) * Math.sign(b);
      return b;
    },
    getCurSpeed(a, b) {
      return Math.abs(a) > Math.abs(j * b) ? l * this.avgSpeed : k * this.avgSpeed;
    },

    resize() {
      this.timer && cancelAnimationFrame(this.timer);
      this._calcTabs();
      this._initCanvas(this.canvas, this.width, this.height);
      this.draw(0);
    },
  },
};
</script>

<style lang="stylus" scoped>
.nav-main {
  position: relative;
  width: 100%;
  white-space: nowrap;
  user-select: none;

  > canvas {
    position: absolute;
    z-index: -1;
    left: 0;
    width: 994px;
    height: 62px;
  }
}

.nav-span {
  display: inline-block;
  font-size: 14px;

  .nav-link {
    padding: 5px 40px 0;
    text-decoration: none !important;
    display: block;
    width: auto;
    min-width: 80px;
    line-height: 40px;
    text-align: left;
    cursor: pointer;
    color: #b9c2cc;
    transition: color 0.2s;
  }

  &.active, &:hover {
    .nav-link {
      color: #fff;
    }
  }
}
</style>
