<template>
  <iframe ref="bhFrameThreeview" :name="frameId" :src="dataset.app.src" style="width: 100%; height: 100%; border: 0" frameborder="0"></iframe>
</template>

<script>
import { uuid } from 'vue-uuid';

export default {
  props: {
    name: String,
    dataset: {
      type: Object,
      default() {
        return {
          app: {
            src: '', // 3d 页面地址

            pid: '', // 3d 项目ID
            current: '', //  当前层级
            layeredNavigation: false, //  层级导航
            nameplate: false, //  铭牌展示
            autoRotate: false, //  自动旋转
            fullScreen: false, //  全屏
            visualAngle: '', //  视角
            inspectionType: '', //  巡检方式
            pathId: '', //  路径ID
            viewpointAnimation: '', //  视点动画

            alarmEvent: false, //  告警事件
            eventLevel: '', // 事件筛选
          },
        };
      },
    },
  },
  data() {
    return {
      readyState: '',
      frameId: uuid.v1(),
      current: this.dataset.app.current, //  缓存当前层级
      nodes: [], // 模型结构
      viewAnimates: [], //  视点列表
    };
  },
  watch: {
    'dataset.app': {
      deep: true,
      handler: function (newValue) {
        this.postMessage('updateAppInfo', newValue);
      },
    },
  },
  created() {
    // 接收ThingJS页面传送的数据
    window.addEventListener('message', this.onMessage);
  },
  beforeDestroy() {
    window.removeEventListener('message', this.onMessage);
  },
  methods: {
    focusNode(newValue) {
      this.postMessage('focusNode', newValue);
    },
    startDrawPath() {
      this.postMessage('startDrawPath');
    },
    stopDrawPath() {
      this.postMessage('stopDrawPath');
    },
    updateAppInfo() {
      this.postMessage('updateAppInfo', this.dataset.app);
    },

    // 路径绘制完成后，3d界面调用
    addDrewPath(pathInfo) {
      this.$properties.addDrewPath(pathInfo);
    },
    onReady(data) {
      if (!this.readyState) {
        this.dataset.app.pid = data.pid;

        //? 在切换层级后这两个属性会失效，延迟处理再设置一遍值
        this.dataset.app.visualAngle = 0;
        this.dataset.app.autoRotate = false;

        this.updateAppInfo();

        this.viewAnimates = data.viewAnimates;
        this.nodes = data.nodes;

        setTimeout(() => {
          this.dataset.app.visualAngle = data.visualAngle;
          this.dataset.app.autoRotate = data.autoRotate;
        }, 2000);

        this.readyState = 'active';
      }
    },
    onMessage(e) {
      const { id, type, data } = e.data;
      if (this.frameId === id) {
        switch (type) {
          case 'updateAppInfo':
            // 收到事件代表3D程序加载完成
            this.onReady(data);
            break;
          case 'levelChange':
            this.current = data;
            break;
          default:
            this[type]?.call(this, data);
            break;
        }
      }
    },
    postMessage(type, data) {
      this.$refs.bhFrameThreeview.contentWindow.postMessage({ type, data }, '*');
    },
  },
};
</script>
