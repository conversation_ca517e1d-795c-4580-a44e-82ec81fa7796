import Vue from 'vue';

import jQuery from 'jquery';

import axios from 'axios';

import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';

import SauUI from 'sau-ui';
import 'sau-ui/dist/sau-ui.css';

import * as echarts from 'echarts';
import VChart from 'vue-echarts';

import * as resizeDetector from 'resize-detector';

Vue.component('v-chart', VChart);

// 更改ElementUI的一些不喜欢的默认配置
ElementUI.Dialog.props.modalAppendToBody.default = true;
ElementUI.Dialog.props.closeOnClickModal.default = false;

export default {
  axios,
  echarts,
  resizeDetector,
  ElementUI,
  SauUI,
  VChart,
  Vue,
  jQuery: jQuery,
  $: jQuery,
};
