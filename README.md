# bh-dashboard

## 介绍

隶属于南京碧慧可视化项目，负责前端展示部分，将数据与可视化组件进行连接，以自定义的最佳角度呈现数据，一目了然，实用，易用，好用。

![](D:\项目\C_数据可视化\数据可视化用例.jpg)

## 概念

在本可视化系统中，一个成品**可视化**由若干个**看板**组成，看板又由若干个**面板**组合而成。

面板是组件在看板上具体实现。

看板可以直接作为视觉输出，具体使用视实际情况而定。

### 看板

​	杂志看板 （即首页型看板，各面板组件依次排列，不遮挡）

​	灵活看板 （大屏常用，面板组件自由拖动，有次序，可互相遮挡）

### 可视化组件

​	默认组件 （系统默认的组件，可以通过二开来增加场景默认组件）

​	插件组件 （引入插件时，包含在插件包内的常用组件）

​	自定义组件 （保存自由配置的组件、在自定义脚本设计的组件）

## 功能

- [x] 可视化编辑器
- [x] ​	面板管理
- [x] ​	主题控制
- [x] ​	布局控制
- [x] ​	样式控制
- [ ] 组件
- [ ] ​	组件管理
- [x] 看板
- [x] ​	看板管理
- [x] ​	导入导出
- [x] 可视化管理
- [ ] 权限管理
- [ ] 插件模块
- [ ] 控件设置
- [ ] 数据源配置
- [x] 版本自适配

## 安装

### npm

    npm install bh-dashboard --save

## 更新日志

### 1.1.1 （2021年3月31日）

```
可视化项目启动。
可视化管理。
我的组件。
可视化编辑器。
Bug fixes and improvements.
```

### 1.1.2 （2021年6月21日）

```
提供主题。
提供样式ID选择器。
提供动态背景。
编辑器ctrl+s 快捷保存。
组件可筛选。
取消请求超时时间。
提供设置面板保存功能。
更改看板类型文字。
内置默认背景图片。
提供组件目录注册。
解决了一些已知问题。
```

### 1.1.12 （2023年3月14日）

```
新增组件刷新功能，同步按钮内文字可配。
提供组件默认配置视图
添加编辑移动模式
添加面板层级次序调整
添加单个面板锁定功能
取消辅助按钮功能（不实用）
面板可放大
默认标题可调整大小
增加视图锁定功能
告警可配置
增加告警语音控制
打开大屏时电脑不休眠
暂时取消预览功能（用的较少），改为链接地址复制
增加组件更改功能
增加开发版本控制
预加载设备详情弹框
更改原自适应为4种不同适应方式
更改组件列表为多级组件

新增默认组件：
	地图组件
	3D组件
	增加页面组
	增加文本组件

优化：
	解决可视化设计时保存后不缩放的问题
	面板与属性联动优化
	可视化面板取消最小宽高
	适配火狐42版本
	修复面板能设置为0宽高的程序BUG
	修复双击组件没有置顶的BUG

默认配置：
	默认每10分钟刷新数据

```

