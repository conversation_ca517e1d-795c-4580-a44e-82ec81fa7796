.dashboard-layout {
  .dashboard-text.title {
    position: relative;
    height: 100%;
    background-color: #fba02b;
    //text-shadow: 0px 0px 3px #aaa;

    &::before, &::after {
      content: '';
      position: absolute;
      top: 0;
      background: #fba02b;
      width: 30px;
      height: 100%;
    }

    &::before {
      left: -29px;
      clip-path: polygon(0% 0%, 100% 0%, 100% 100%);
    }

    &::after {
      right: -29px;
      clip-path: polygon(0% 0%, 100% 0%, 0% 100%);
    }
  }

  .sau-panel {
    height: 100%;
    border: 0px;

    .sau-panel--header {
      position: relative;
      padding-left: 0;
      height: 30px;
      line-height: 30px;
      border-bottom: 0;
      background: none;
      overflow: hidden;

      > .sau-panel--title {
        position: relative;
        display: inline-block;
        padding-left: 15px;
        width: 100%;
        font-family: pangmen, yahei, serif;
        font-size: 22px;
        letter-spacing: 5px;
        white-space: nowrap;
        height: 30px;
        line-height: 30px;
        color: #223030;

        &:before {
          content: '';
          display: block;
          position: absolute;
          width: 80%;
          height: 100%;
          background: #fba02b;
          transform: skew(45deg);
          left: 5px;
        }

        > span {
          position: relative;
          z-index: 1;
        }
      }

      &:before {
        content: '';
        display: block;
        position: absolute;
        top: 50%;
        width: 0;
        height: 0;
        border-right: 20px solid transparent;
        border-bottom: 20px solid #fba02b;
      }

      &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        right: 0px;
        width: 0;
        height: 0;
        border-left: 30px solid transparent;
        border-top: 30px solid #503817;
      }
    }

    .sau-panel--body {
      background: none;
    }
  }
}
