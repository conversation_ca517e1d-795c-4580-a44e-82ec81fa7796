import { COLOR_LIST } from '@/constant/designer';
import { getThemePreset } from '@/preset/dashboard/themes';
import { extend } from '@/utils';

function defaultColorList() {
  return [].concat(COLOR_LIST);
}

function defaultStyle() {
  return {
    fontFamily: "Times, 'Times New Roman', Georgia, serif",
    fontSize: '14px',
    lineHeight: '14px',
    color: '#000',
    fontWeight: 'normal',
    letterSpacing: '0px',
    textAlign: 'left',
  };
}

class Appearance {
  mode = 'light'; // 色彩模式
  _theme = ''; // 主题
  _color = defaultColorList(); // 颜色池
  _style = defaultStyle();
  background = {
    color: '',
    image: {
      repeat: 'no-repeat',
      size: 'cover',
      url: '',
    },
    dynamic: '0', // 动态背景
  };

  get theme() {
    return this._theme;
  }

  set theme(value) {
    this._theme = value;

    if (value) {
      const themePreset = getThemePreset(value);

      if (themePreset.color) {
        this.color = themePreset.color;
      }

      extend(true, this, themePreset);
    }
  }

  set color(value) {
    this._color = value || defaultColorList();
  }

  get color() {
    return this._color;
  }

  get style() {
    return this._style;
  }

  set style(value) {
    this._style = value || defaultStyle();
  }
}

export default Appearance;
