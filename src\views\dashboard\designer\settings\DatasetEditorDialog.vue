<template>
  <sau-dialog title="数据配置" :visible.sync="visible" :custom-class="'el-form-item' + (isError ? ' is-error' : '')">
    <el-input v-model="data" type="textarea" @change="handleDatasetChange"></el-input>
  </sau-dialog>
</template>

<script>
export default {
  name: 'DatasetEditorDialog',
  data() {
    return {
      visible: false,
      data: '',
      isError: false,
    };
  },
  methods: {
    show(panel) {
      this.isError = false;
      if (panel?.dataset) {
        this._panel = panel;
        this.data = JSON.stringify(panel.dataset, null, 2);
        this.visible = true;
      } else {
        this.$message.warning('无可配置数据，请选择面板');
      }
    },
    handleDatasetChange(value) {
      try {
        this.isError = false;
        this._panel.dataset = JSON.parse(value);
      } catch (e) {
        console.error(e);
        this.isError = true;
      }
    },
  },
};
</script>

<style scoped>
>>> .el-textarea,
>>> .el-textarea > .el-textarea__inner {
  height: 100%;
}
</style>
