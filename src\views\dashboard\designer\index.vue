<template>
  <div class="dashboard-designer">
    <!-- 头部控制区 -->
    <div class="designer-header">
      <span v-if="available">
        <el-link type="info" :underline="false" @click="open">{{ coreData.name }}</el-link>
        <el-link :underline="false" @click="copyToClipboard">
          <el-tooltip class="item" effect="dark" content="复制链接" placement="bottom">
            <i class="el-icon-link" style="margin-left: 10px"></i>
          </el-tooltip>
        </el-link>
      </span>
      <span v-else>未保存</span>

      <span style="float: left; margin-left: 2.5px">
        <el-tooltip class="item" effect="dark" content="看板管理" placement="bottom-start">
          <router-link to="/">
            <DButton class="design-btn-menu">
              <i class="el-icon-menu"></i>
            </DButton>
          </router-link>
        </el-tooltip>

        <el-tooltip class="item" effect="dark" content="撤销" placement="bottom-start">
          <DButton class="design-btn-menu" :disabled="!history.hasPrevious()" style="margin-left: 40px" @click="undo">
            <i class="el-icon-refresh-left"></i>
          </DButton>
        </el-tooltip>

        <el-tooltip class="item" effect="dark" content="还原" placement="bottom-start">
          <DButton class="design-btn-menu" :disabled="!history.hasNext()" style="margin-left: 5px" @click="redo">
            <i class="el-icon-refresh-right"></i>
          </DButton>
        </el-tooltip>
      </span>

      <span style="float: right">
        <el-tooltip class="item" effect="dark" content="刷新" placement="bottom">
          <DButton size="mini" type="info" round class="btn-refresh" @click.native="refresh"><i class="el-icon-refresh"></i></DButton>
        </el-tooltip>

        <el-tooltip class="item" effect="dark" :content="saveState" placement="bottom">
          <DButton type="success" size="mini" @click.native="saveDashBoard(true)">
            <i class="el-icon-s-promotion"></i>
          </DButton>
        </el-tooltip>

        <el-tooltip class="item" effect="dark" content="看板配置" placement="bottom" style="margin-left: 20px">
          <DButton size="mini" @click.native="(designerData.ctrlView = '2'), clearSelection()">
            <i class="el-icon-tickets"></i>
          </DButton>
        </el-tooltip>

        <el-dropdown @command="handleCtrlViewChanged">
          <DButton>视图</DButton>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="1" :icon="designerData.ctrlView === '1' ? 'el-icon-check' : ''">组件列表</el-dropdown-item>
            <el-dropdown-item command="2" :icon="designerData.ctrlView === '2' ? 'el-icon-check' : ''">属性视图</el-dropdown-item>
            <el-dropdown-item command="3" :icon="designerData.ctrlView === '3' ? 'el-icon-check' : ''">面板管理</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tooltip class="item" effect="dark" content="设置" placement="bottom" @click.native="openSettingsDrawer">
          <DButton><i class="el-icon-s-tools"></i></DButton>
        </el-tooltip>
      </span>
    </div>

    <!-- 主设计面板 -->
    <div class="designer-main">
      <sau-scrollbar style="height: 100%" class="canvas-wrapper" @mousedown.native="clearSelection()">
        <DMenuWrapper>
          <DContextmenu class="design-context" :class="[theme, mode, coreData.custom.className]" :style="canvasWrapperStyl">
            <div :style="{ transform: `scale(${canvasScale})` }" style="transform-origin: left top">
              <DesignView
                ref="design_view"
                :designer="designerData"
                :dashboard="coreData"
                :panels="panels"
                @onSelectPanel="setSelection"
              ></DesignView>
            </div>

            <template slot="contextmenu">
              <DMenuitem label="刷新" @click="refresh" />
              <div class="bt-separator">
                <template v-if="hasSelection">
                  <DMenuitem label="剪切" @click="execute('cut')" />
                  <DMenuitem label="复制" @click="execute('copy')" />
                </template>
                <DMenuitem label="粘贴" @click="execute('paste')" />
              </div>
              <template v-if="hasSelection">
                <div v-if="coreData.type !== 1" class="bt-separator">
                  <DMenuitem label="置顶" @click="execute('moveUpPanel')" />
                  <DMenuitem label="置底" @click="execute('moveDownPanel')" />
                </div>
                <DMenuitem label="更换" class="bt-separator" @click="execute('replacePanel', selection)" />
                <DMenuitem label="删除" class="bt-separator" @click="execute('deletePanel', selection)" />
              </template>
            </template>
          </DContextmenu>
        </DMenuWrapper>
      </sau-scrollbar>

      <!-- 其它辅助面板 -->
      <Toolbar :mode.sync="designerData.mode" />
      <BBar :designer="designerData" :dashboard="coreData" :panels="panels" />

      <div class="designer-view-ctrl">
        <keep-alive>
          <transition name="slide-fade">
            <ComponentsList v-if="designerData.ctrlView == '1'" @onAddPanel="handleAddPanel" @selected="handleComponentListSelected"></ComponentsList>
            <Properties
              v-else-if="designerData.ctrlView == '2'"
              :designer="designerData"
              :dashboard="coreData"
              :panels="panels"
              :selection="selection"
            ></Properties>
            <Layers v-else-if="designerData.ctrlView == '3'" :list="panels" :current="selection"></Layers>
          </transition>
        </keep-alive>
      </div>

      <!-- 看板设置 -->
      <SettingDrawer ref="settings" :dashboard="coreData" :logs="logs" :panels="panels"></SettingDrawer>
      <!-- 数据选择器 -->
      <DataSourceSelectorDialog ref="dataSourceSelector"></DataSourceSelectorDialog>
      <!-- 数据集编辑器 -->
      <DatasetEditorDialog ref="datasetEditor"></DatasetEditorDialog>
      <!-- iframe界面对话框 -->
      <FrameDialog />
    </div>
  </div>
</template>

<script>
import design from 'sau-design';
import domToImage from 'dom-to-image';

import { copyTextToClipboard, dataURLtoFile, debounce } from '@/utils';

import command from '@/mixins/command';
import * as DesignerCommand from './commands';

import core from '../player/core';
import shortcuts from '@/mixins/shortcuts';
import log from '@/mixins/log';
import { DISIGNER_MODE } from '@/constant/designer';

import components from './components';
import DesignView from './DesignView';

import Panel from '@/models/Panel';
import SettingDrawer from './settings/SettingDrawer';
import DatasetEditorDialog from './settings/DatasetEditorDialog';
import DataSourceSelectorDialog from './settings/dataSourceSelector/DataSourceSelectorDialog';

import { importVBoard, saveFile, saveVBoard } from '@/api';
import { compare } from 'compare-versions';
import FrameDialog from '@/dialogs/FrameDialog.vue';

const { DButton, DMenu } = design;
const { DMenuWrapper, Contextmenu: DContextmenu, Menuitem: DMenuitem } = DMenu;

export default {
  name: 'DashboardDesigner',
  components: {
    FrameDialog,
    DButton,
    DesignView,
    ...components,
    DMenuWrapper,
    DContextmenu,
    DMenuitem,
    SettingDrawer,
    DatasetEditorDialog,
    DataSourceSelectorDialog,
  },
  mixins: [core, command, shortcuts, log],
  data() {
    return {
      designerData: {
        mode: DISIGNER_MODE.MOVE,
        ctrlView: '2', // 控制视图： 1：组件列表  2：属性视图  3：面板管理
        lock: true,
        scale: 100,
        mouseX: 0,
        mouseY: 0,
      },
      selection: [],
      componentListSelected: null, // 组件列表选择项
    };
  },
  computed: {
    hasSelection() {
      return this.selection.length > 0;
    },
    saveState() {
      return this.available ? '保存' : '发布';
    },
    canvasScale() {
      return this.designerData.scale / 100;
    },
    canvasWrapperStyl() {
      return {
        width: this.coreData.size[0] * this.canvasScale + 'px',
        height: this.coreData.size[1] * this.canvasScale + 'px',
      };
    },
  },
  watch: {
    'coreData.type'(value) {
      this.clearPanels();
      if (value !== 0) {
        this.designerData.scale = 100;
      }
    },
    'coreData.editable'(value) {
      this.panels.forEach((panel) => (panel.editable = value));
    },
  },
  beforeCreate() {
    this.loading = this.$loading({
      lock: true,
      text: '拼命加载中...',
      spinner: 'el-icon-loading',
      customClass: 'designer-loading',
      background: 'rgba(0, 0, 0, 0.7)',
    });
  },
  created() {
    const {
      params: { dashboard },
    } = this.$route;

    this.$on('onLoaded', (dashboard) => {
      const { size } = dashboard;
      const { clientWidth, clientHeight } = this.$el;

      this.designerData.scale = Math.min(((clientWidth - 400) * 100) / size[0], ((clientHeight - 180) * 100) / size[1]);
    });

    this.bindShortcuts('ctrl+x', () => this.execute('cut'));
    this.bindShortcuts('ctrl+c', () => this.execute('copy'));
    this.bindShortcuts('ctrl+v', () => this.execute('paste'));
    this.bindShortcuts('ctrl+z', this.undo);
    this.bindShortcuts(
      'ctrl+s',
      () => {
        this.saveDashBoard(true);
        return false;
      },
      { global: true }
    );

    this.load(dashboard).finally(() => {
      setTimeout(() => {
        this.loading.close();
        this.log('看板编辑器完成加载');
      }, 1000);
    });
  },
  beforeDestroy() {
    this.unbindShortcuts('ctrl+x');
    this.unbindShortcuts('ctrl+c');
    this.unbindShortcuts('ctrl+v');
    this.unbindShortcuts('ctrl+z');
    this.unbindShortcuts('ctrl+s');
  },
  methods: {
    // start 选择项操作
    setSelection: debounce(
      function (selection) {
        this.selection = Array.isArray(selection) ? selection : [selection];
        if (!this.designerData.lock) {
          this.designerData.ctrlView = '2';
        }
      },
      100,
      {
        leading: true,
        trailing: false,
      }
    ),
    getSelection() {
      return this.selection;
    },
    clearSelection() {
      this.setSelection([]);
    },
    // end 选择项操作

    // 添加内容
    addContent(items = []) {
      items.forEach((item) => {
        if (item instanceof Panel) {
          item.regenerateId();
          this.addPanel(item);
        }
      });
    },
    // 移除内容
    deleteContent(items = []) {
      items.forEach((item) => {
        if (item instanceof Panel) {
          this.deletePanel(item);
        }
      });
    },

    //  编辑器操作
    execute(type, arg1) {
      let command;
      if (type === 'cut') {
        command = new DesignerCommand.CutCommand();
      } else if (type === 'copy') {
        command = new DesignerCommand.CopyCommand();
      } else if (type === 'paste') {
        command = new DesignerCommand.PasteCommand();
      } else if (type === 'addPanel') {
        command = new DesignerCommand.AddPanelCommand();
        command.data = arg1;
      } else if (type === 'replacePanel') {
        command = new DesignerCommand.ReplacePanelCommand();
      } else if (type === 'deletePanel') {
        command = new DesignerCommand.DeletePanelCommand();
      } else if (type === 'moveUpPanel') {
        command = new DesignerCommand.MoveUpPanelCommand();
      } else if (type === 'moveDownPanel') {
        command = new DesignerCommand.MoveDownPanelCommand();
      } else if (type === 'changeType') {
        command = new DesignerCommand.ChangeTypeCommand();
      } else if (type === 'changeProperty') {
        command = new DesignerCommand.ChangePropertyCommand();
      } else if (type === 'importDashboard') {
        command = new DesignerCommand.ImportDashboardDashboard();
      } else if (type === 'clear') {
        command = new DesignerCommand.ClearCommand();
      } else if (type === 'reset') {
        command = new DesignerCommand.ResetCommand();
      }

      if (command) {
        this.executeCommand(command);
      }
    },

    openSettingsDrawer() {
      this.$refs.settings.open();
    },
    showDatasetDialog(panel) {
      this.$refs.datasetEditor.show(panel);
    },
    showDataSourceSelector(dataSource) {
      this.$refs.dataSourceSelector.show(dataSource);
    },

    handleAddPanel(item) {
      item.editable = this.coreData.editable;

      if (this.coreData.type === 1) {
        item.w = this.widthToGridWidth(item.width, this.coreData);
        item.h = this.heightToGridHeight(item.height, this.coreData);
      } else {
        item.w = item.width;
        item.h = item.height;
      }

      this.execute('addPanel', item);
    },
    handleCtrlViewChanged(viewId) {
      this.designerData.ctrlView = viewId;
      // this.selectPanel();
    },
    handleComponentListSelected(selection) {
      this.componentListSelected = selection;
    },

    getMaxZIndex() {
      return (
        Math.max.apply(
          this,
          this.panels.map((panel) => panel.zIndex)
        ) + 1
      );
    },
    getMinZIndex() {
      return (
        Math.min.apply(
          this,
          this.panels.map((panel) => panel.zIndex)
        ) - 1
      );
    },
    /**
     * 看板内导入，这里只用添加导入数据中的面板
     */
    importDashboard(data) {
      importVBoard({
        partial: '1',
        file: data.raw,
      })
        .validate()
        .then(({ data }) => {
          if (data.elements?.length > 0) {
            data.elements.forEach((e) => {
              this.addPanel(e);
            });
            this.$message.success('导入数据成功');
          } else {
            this.$message.info('无可导入数据');
          }
        });
    },

    adaptToVersion(data) {
      if (data.version) {
        // 低于1.1.0版本
        if (compare(data.version, '1.1.0', '<=')) {
          data.autofit = data.autofit == 1;
        }

        // 低于1.1.12版本
        if (compare(data.version, '1.1.12', '<=')) {
          data.custom.color = JSON.stringify(data.custom.color);
        }
      }
      return data;
    },
    getDataModel() {
      const { id, name, type, size, fitMode, timezone, autoRefresh, editable, layout, tools, appearance, custom, version, createTime, updateTime } =
        this.coreData;

      const backgroundImage = appearance.background.image;

      const dataModel = {
        createTime,
        updateTime,
        // remark: null,
        name,
        type,
        size,
        autofit: fitMode,
        timezone,
        autoRefresh,
        editable,
        background: appearance.background.color,
        fontStyle: appearance.style,
        backgroundImage: {
          repeat: backgroundImage.repeat,
          size: backgroundImage.size,
          url: backgroundImage.url,
        },
        desc: '',
        custom: {
          tools: tools.enable,
          mode: appearance.mode,
          color: appearance.color,
          theme: appearance.theme,
          idPrefix: custom.idPrefix,
          styles: custom.style,
          className: custom.layoutClass,
          dynamic: appearance.background.dynamic,
          version,
        },
        grid: layout,
        // alarm: {
        //   voice: false,
        //   enable: false,
        //   interval: 60,
        // },
        elements: this.panels.map(({ id, name, type, wrapped, editable, visible, x, y, w, h, dataset, createTime, updateTime }) => {
          return {
            id,
            name,
            type,
            x,
            y,
            w,
            h,
            wrapped,
            editable,
            visible,
            dataset,
            // moved: false,
            // remark,
            createTime,
            updateTime,
          };
        }),
      };

      if (id) {
        dataModel.id = id;
      }

      this.adaptToVersion(dataModel);

      return dataModel;
    },

    /**
     * 保存看板
     * @param quickSave 忽略生成预览图，快速保存
     * @param isSaveAs 另存为
     */
    async saveDashBoard(quickSave, isSaveAs) {
      const dataModel = this.getDataModel();
      let operationText;

      if (isSaveAs) {
        dataModel.id = '';
        operationText = '另存为';
      } else {
        operationText = this.saveState;
      }

      if (!dataModel.name) {
        this.openSettingsDrawer();
        this.$message({
          message: '请完善看板信息后' + operationText,
        });
        return;
      }

      this.saving = this.$loading({
        lock: true,
        text: '准备' + operationText + '看板...',
        spinner: 'el-icon-loading',
        customClass: 'designer-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      this.log(`${operationText}看板：${dataModel.name}`, 'primary');

      const backgroundImage = this.coreData.appearance.background.image;
      if (backgroundImage.url && backgroundImage.url.raw) {
        this.saving.text = '正在保存背景图...';
        const { name, raw } = backgroundImage.url;
        await saveFile({ name, file: raw })
          .then(({ data }) => {
            dataModel.backgroundImage.url = data.id;
          })
          .catch(() => {
            this.log('系统错误：保存看板背景图异常', 'error');
          });
      }

      this.saving.text = '正在' + operationText + '看板...';
      const saveState = await saveVBoard(dataModel)
        .then(({ data }) => {
          const { id } = data;
          dataModel.id = id;
          return true;
        })
        .catch(() => {
          this.log('系统错误：保存看板数据异常', 'error');
          return false;
        });

      if (quickSave !== true) {
        this.saving.text = '正在准备预览图...';
        let previewImage;
        await domToImage
          .toJpeg(this.$refs.design_view.$el)
          .then(function (dataUrl) {
            previewImage = dataURLtoFile(dataUrl, 'preview');
          })
          .catch(() => {
            this.log('系统错误：生成预览图异常', 'error');
          });

        if (previewImage) {
          this.saving.text = '正在保存预览图...';
          await saveFile({
            id: dataModel.id,
            name: 'preview.png',
            file: previewImage,
          }).catch(() => {
            this.log('系统错误：保存预览图异常', 'error');
          });
        }
      }
      this.saving.close();

      if (saveState) {
        if (isSaveAs) {
          this.$confirm('是否留在当前看板?', '提示', {
            confirmButtonText: '是',
            cancelButtonText: '否',
            type: 'info',
          }).catch(() => {
            this.$router.push({
              name: 'designerx',
              params: { id: dataModel.id },
            });
          });
        } else {
          // 如果保存成功则刷新看板
          if (this.available) {
            this.refresh().then(() => {
              this.$message({
                message: operationText + '成功',
                type: 'success',
              });
            });
          } else {
            this.$router.push({
              name: 'designerx',
              params: { id: dataModel.id },
            });
          }
        }
      }
    },

    refresh(hasNotified) {
      if (!this.available && hasNotified !== true) {
        this.$confirm('看板已经被更改，继续刷新吗？', '提示', {
          cancelButtonText: '刷新，不保存',
          confirmButtonText: '取消',
          type: 'info',
        }).catch(() => {
          this.refresh(true);
        });
        return;
      }

      this.loading = this.$loading({
        lock: true,
        text: '正在刷新看板...',
        spinner: 'el-icon-loading',
        customClass: 'designer-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });
      const dashboardId = this.coreData.id;
      const { designerData, logs } = this.$data;
      Object.assign(this.$data, this.$options.data(), { designerData, logs });
      this.coreData.id = dashboardId;
      return this.load().finally(() => {
        setTimeout(() => {
          this.loading.close();
          this.log('刷新看板');
        }, 1000);
      });
    },
    copyToClipboard() {
      try {
        const { href } = this.$router.resolve({
          name: 'dashboard',
          params: { id: this.coreData.id },
        });
        copyTextToClipboard(location.pathname + href);
        this.$message.success('复制地址到剪贴板成功');
      } catch (e) {
        this.$message.error('复制地址到剪贴板失败');
      }
    },
  },
};
</script>
