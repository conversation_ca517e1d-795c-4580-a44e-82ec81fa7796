<template>
  <div v-show="panel.visible" class="dashboard-view">
    <sau-dialog v-if="panel.wrapped === '1'" :title="panel.name" :append-to-body="true" force-middle :visible.sync="panel.visible">
      <component :is="loadComponent()" ref="view" :dataset="panel.dataset"></component>
    </sau-dialog>
    <sau-panel v-else-if="panel.wrapped" :title="panel.name">
      <component :is="loadComponent()" ref="view" :dataset="panel.dataset"></component>
    </sau-panel>
    <component :is="loadComponent()" v-else ref="view" :dataset="panel.dataset"></component>
    <div v-if="mask" class="dashboard-view-mask"></div>
  </div>
</template>

<script>
import { getComponent } from '@/myComponents';

import { setRef, deleteRef } from './core/refs';
import ErrorComponent from './ErrorComponent';

export default {
  name: 'DashboardView',
  props: {
    panel: {
      type: Object,
      require: true,
    },
    mask: {
      type: Boolean,
      default: false,
    },
  },
  provide() {
    return {
      panel: this.panel,
    };
  },
  watch: {
    'panel.type': {
      immediate: true,
      handler: function () {
        this.$nextTick(() => {
          setRef(this.panel, this.$refs.view);
        });
      },
    },
  },
  mounted() {
    const titleDom = this.$el.querySelector('.sau-panel--title');
    if (titleDom) {
      titleDom.addEventListener('dblclick', () => this.$el.parentNode.classList.toggle('fill'));
    }
  },
  beforeDestroy() {
    deleteRef(this.panel);
  },
  methods: {
    loadComponent() {
      const { type } = this.panel;
      let component;

      try {
        if (typeof type === 'string') {
          component = getComponent(type).component;
        } else if (typeof type === 'object') {
          component = type;
        }
      } catch (e) {
        component = ErrorComponent;
      }

      return component;
    },
    startAnimate() {
      this.$refs.view.animate = true;
    },
    stopAnimate() {
      this.$refs.view.animate = false;
    },
    load() {
      try {
        const loadFunc = this.$refs.view.load;
        if (typeof loadFunc === 'function') {
          loadFunc.call();
        }
      } catch (e) {
        console.error('面板数据加载异常，请打开编辑器调试');
      }
    },
  },
};
</script>
