import framework from 'sau-framework';
import { MessageBox } from 'element-ui';

import JSEncrypt from 'jsencrypt';
import qs from 'qs';
import * as user from '../api/user';

function getPublicKey() {
  return user
    .getPublicKey()
    .then((response) => {
      if (!response.publicKey) {
        throw new Error('publicKey error.');
      } else {
        return response.publicKey;
      }
    })
    .catch((error) => {
      throw error;
    });
}

function encryptLogin(username, password, publicKey, verificationCode = '') {
  let encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey);
  username = escape(username.trim());
  password = encryptor.encrypt(password);
  return user
    .login(
      qs.stringify({
        'sysUser.loginName': username,
        'sysUser.password': password,
        verificationCode,
      })
    )
    .then((response) => {
      const result = {
        status: false,
        token: '',
        message: '登录失败',
      };

      function successLogin(result) {
        result.status = true;
        // 后台使用session管理状态,暂时使用时间戳代替
        result.token = new Date().getTime();
        return result;
      }

      switch (response) {
        case 'success':
          successLogin(result).message = '登录成功';
          break;

        case 'noLoginName':
          result.message = '用户名不存在';
          break;
        case 'verificationCode-error':
          result.message = '验证码错误';
          break;
        case 'delay':
          result.message = '登录失败次数过多，请稍后再试';
          break;
        case 'lock':
          result.message = '登录失败次数过多，此账号已被锁定';
          break;
        case 'success-toChangPassword':
          successLogin(result).message = '开启强制首次登录改密，提醒用户修改密码';
          break;
        case 'success-expire-toChangPassword':
          successLogin(result).message = '距离密码有效期不到一周，请及时修改密码';
          MessageBox.alert('距离密码有效期不到一周，请及时修改密码', '提示', {
            confirmButtonText: '确定',
            type: 'warning',
          });
          break;
        case 'invalid':
          result.message = '当前账号密码已过期';
          break;

        case 'failure':
          result.message = '用户名密码错误，请重新输入';
          break;
        case 'macerr':
          result.message = 'IP或MAC错误';
          break;
        case 'expire':
          result.message = '当前用户过期';
          break;

        default:
          result.message = '无效状态';
          break;
      }

      return result;
    })
    .catch((error) => {
      return {
        status: false,
        message: '程序异常',
      };
    });
}

function login({ username, password, verificationCode }) {
  return getPublicKey().then((publicKey) => {
    return encryptLogin(username, password, publicKey, verificationCode);
  });
}

function getInfo() {
  return user.getInfo().then((response) => {
    const { result, items } = response;
    const res = {
      status: false,
      userInfo: {
        username: '未登录用户',
        roles: [],
      },
    };

    if (result === 'success') {
      const { userName, roles = ['admin'], status } = items;
      res.status = true;
      res.userInfo = {
        username: userName,
        roles,
      };
      return res;
    } else if (typeof response === 'string' && response.indexOf('logout') > -1) {
      res.status = 1;
      res.message = '用户已登出，请重新登录';
    } else {
      res.message = '系统异常： 获取用户信息失败';
    }
    return res;
  });
}

function logout() {
  return user.logout().then(() => {
    return {
      status: true,
      message: '退出成功',
    };
  });
}

framework.user.login = login;
framework.user.getInfo = getInfo;
framework.user.logout = logout;
