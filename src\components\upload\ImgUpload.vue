<template>
  <div tabindex="0" class="el-upload-list el-upload-list--picture-card">
    <li class="el-upload-list__item preview-item">
      <img ref="preview" class="el-upload-list__item-thumbnail" :src="file.url" alt="" @error="handleError" />
      <slot :file="file"></slot>
    </li>
    <input ref="input" class="el-upload__input" type="file" :name="name" :accept="accept" @change="handleChange" />
  </div>
</template>

<script>
import noimg from '@/assets/images/designer/preview-empty.png';

export default {
  name: 'ImgUpload',

  props: {
    type: String,
    action: {
      type: String,
      // required: true
    },
    name: {
      type: String,
      default: 'file',
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.gif',
    },
    file: Object,
    onChange: {
      type: Function,
      default: function () {},
    },
  },
  mounted() {
    const reader = new FileReader();
    reader.addEventListener(
      'load',
      () => {
        this.$refs.preview.src = reader.result;
      },
      false
    );
    this.reader = reader;
  },

  methods: {
    select() {
      this.$refs.input.click();
    },
    handleChange(ev) {
      const files = ev.target.files;

      if (!files) return;
      this.reader.readAsDataURL(files[0]);
      this.onChange(files[0]);
    },
    handleError({ target }) {
      target.src = noimg;
    },
  },
};
</script>
<style lang="stylus" scoped>
.preview-item {
  margin: 0;
  width: 100%;
  border: 1px solid #f0f0f0;
  border-radius: 0;
  background-color: #F0F0F0;
}

.el-upload-list--picture-card .el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
