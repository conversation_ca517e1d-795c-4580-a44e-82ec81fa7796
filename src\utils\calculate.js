/**
 * Mb (全称Mbps) 是电信部门衡量网络带宽的单位 (兆 比特位 每秒)
 * MB (Mbytes) 是单位 (兆 字节)
 */
const CAPACITY_SIZE_BIT = ['Byte', 'Kb', 'Mb', 'Gb', 'Tb', 'Pb', 'Eb', 'Zb', 'Yb', 'Bb', 'Nb', 'Db'];
const CAPACITY_SIZE = ['Byte', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB', 'BB', 'NB', 'DB'];

const CAPACITY_SIZE_U = CAPACITY_SIZE.map((_) => _.toUpperCase());

/**
 * Windows系统中仍在大量使用公制前缀的二进制写法，而世界上90% 的个人计算机在使用win系统。
 * 随机存取存储器容量，如主存储或CPU缓存的大小，因为存储的物理地址的原因，也在使用二进制千字节。
 */
const SIZE_DIVISOR_KILO = 1024;

/**
 * 表示计算机网络、内部总线、硬盘驱动器和闪存的介质传输速度，以及大部分存储器的容量，特别是硬盘容量，闪存容量和数字多功能影音光盘
 * iB
 */
const SIZE_DIVISOR = 1000;

/**
 * 网络容量计算方法
 * ★最小转换单位为Byte
 * 注： 网络单位计算方法需要对单位进行bit转换操作，请自行 /8
 * @param {*} value 需要被转换的值
 * @param {string} from 需要被转换的单位，默认：Byte
 * @param {string} to 转换的单位（接受auto自动判断），默认：Gb
 * @param {number} decimals 保留小数，默认：0
 * @param {boolean} format 格式化数据，默认：false
 * @param {Array} unit 单位，默认：b
 * @param {number} divisor 底数，默认：1024
 */
export function calculateNetSize(
  value,
  from = 'Byte',
  to = 'Gb',
  decimals = 0,
  format = true,
  unit = CAPACITY_SIZE_BIT,
  divisor = SIZE_DIVISOR_KILO
) {
  return calculateCapacitySize(value, from, to, decimals, format, unit, divisor);
}

/**
 * 磁盘容量计算方法
 * ★最小转换单位为Byte
 * @param {*} value 需要被转换的值
 * @param {string} from 需要被转换的单位，默认：Byte
 * @param {string} to 转换的单位（接受auto自动判断），默认：GB
 * @param {number} decimals 保留小数，默认：0
 * @param {boolean} format 格式化数据，默认：false
 * @param {Array} unit 单位，默认：B
 * @param {number} divisor 底数，默认：1024
 */
export function calculateDiskSize(value, from = 'Byte', to = 'GB', decimals = 0, format = true, unit = CAPACITY_SIZE, divisor = SIZE_DIVISOR_KILO) {
  return calculateCapacitySize(value, from, to, decimals, format, unit, divisor);
}

/**
 * 通用容量计算方法
 * ★最小转换单位为Byte
 * @param {*} value 需要被转换的值
 * @param {string} from 需要被转换的单位，默认：Byte
 * @param {string} to 转换的单位（接受auto自动判断），默认：GB
 * @param {number} decimals 保留小数，默认：0
 * @param {boolean} format 格式化数据，默认：false
 * @param {Array} unit 单位，默认：B
 * @param {number} divisor 底数，默认：1000
 */
export function calculateCapacitySize(value, from = 'Byte', to = 'GB', decimals = 0, format = true, unit = CAPACITY_SIZE, divisor = SIZE_DIVISOR) {
  let result = parseInt(value) || 0;
  const symbol = result < 0 ? -1 : 1;

  if (result !== 0) {
    const fromIndex = CAPACITY_SIZE_U.indexOf(from.toUpperCase());
    result = Math.abs(result);

    let toIndex = -1;

    if (to === 'auto') {
      toIndex = fromIndex + Math.floor(Math.log(result) / Math.log(divisor));
      to = unit[toIndex];
    } else {
      toIndex = CAPACITY_SIZE_U.indexOf(to.toUpperCase());
    }

    if (fromIndex > -1 && toIndex > -1) {
      const i = Math.abs(fromIndex - toIndex);

      if (toIndex > fromIndex) {
        // 正向转换 如： KB => MB
        result = parseFloat(result / Math.pow(divisor, i));
      } else {
        // 其他转换 如： KB => BYTES , KB => KB
        result = parseFloat(result * Math.pow(divisor, i));
      }
    }
  } else {
    if (to === 'auto') {
      to = from;
    }
  }

  decimals = Math.pow(10, decimals);
  result = (Math.round(result * decimals) / decimals) * symbol;

  return format ? result + ' ' + to : result;
}

/**
 *
 * @param {number} value 分数
 * @param {number} total 分母
 * @param {} config 配置项：
 * decimals 保留小数
 * outOfBounds 是否可越界
 * format 格式化数据
 */
export function calculatePercent(value, total = 100, { decimals = 0, allowOutOfBounds = false, format = true, strategy = 'round' } = {}) {
  value = parseFloat(value) || 0;
  total = parseFloat(total) || 100;

  decimals = Math.pow(10, decimals);
  let percent = Math[strategy].call(Math, (value * 100 * decimals) / total) / decimals;

  if (!allowOutOfBounds) {
    percent = percent > 100 ? 100 : percent;
  }

  return format ? percent + '%' : percent;
}

/**
 * 大屏计算百分比数值，默认不格式化，向下取整
 * @param {number} value 分数
 * @param {number} total 分母
 */
export function calculateFloorPercent(value, total, config = {}) {
  return calculatePercent(
    value,
    total,
    Object.assign(
      {
        format: false,
        strategy: 'floor',
      },
      config
    )
  );
}

export function gcd(a, b) {
  if (b == 0) {
    return 1;
  }
  if (a % b === 0) {
    return b;
  }
  return gcd(b, a % b);
}

export function integerRatio(a, b) {
  const c = gcd(a, b);
  return [a / c, b / c];
}
