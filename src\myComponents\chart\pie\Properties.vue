<template>
  <div>
    <sau-panel title="数据源" collapsable virtual-scrollbar>
      <DataSourceProperties :props="dataset.dataSource"></DataSourceProperties>
    </sau-panel>
    <SyncDatasetButton :panel="panel" :source="dataset" @onSyncDataset="onSyncDataset" />
  </div>
</template>

<script>
export default {
  props: {
    panel: Object,
    dataset: Object,
  },
  inject: ['context'],
  methods: {
    onSyncDataset() {
      this.$emit('panel', 'load');
    },
  },
};
</script>
