<template>
  <div :id="elId" class="particles"></div>
</template>

<script>
import 'particles.js';

export default {
  data() {
    return {
      elId: 'particlesId',
    };
  },
  created() {
    this.elId = 'backEffect' + Date.now();
  },
  mounted() {
    window.particlesJS(this.elId, {
      particles: {
        number: {
          value: 100,
          density: {
            enable: true,
            value_area: 1000,
          },
        },
        color: {
          value: ['#fff'],
        },

        shape: {
          type: 'circle',
          stroke: {
            width: 5,
            color: '#434984',
            opacity: 0.6,
          },
          polygon: {
            nb_sides: 5,
          },
        },
        opacity: {
          value: 0.5,
          random: true,
          anim: {
            enable: false,
            speed: 1,
            opacity_min: 0.1,
            sync: false,
          },
        },
        size: {
          value: 2,
          random: true,
          anim: {
            enable: false,
            speed: 1,
            size_min: 0.1,
            sync: false,
          },
        },
        line_linked: {
          enable: true,
          distance: 130,
          width: 1.5,
          color: '#434984',
          opacity: 0.6,
        },
      },
      interactivity: {
        detect_on: 'canvas',
        events: {
          onhover: {
            enable: true,
            mode: 'grab',
          },
          onclick: {
            enable: false,
          },
          resize: true,
        },
        modes: {
          grab: {
            distance: 140,
            line_linked: {
              opacity: 0.7,
            },
          },
          bubble: {
            distance: 400,
            size: 40,
            duration: 2,
            opacity: 8,
            speed: 3,
          },
          repulse: {
            distance: 200,
            duration: 0.4,
          },
          push: {
            particles_nb: 4,
          },
          remove: {
            particles_nb: 2,
          },
        },
      },
      retina_detect: true,
    });
  },
  beforeDestroy() {
    // 销毁 particlesJS
    let pJSDom = window['pJSDom'];
    if (pJSDom && pJSDom.length > 0) {
      pJSDom.forEach((pJSDomItem) => {
        pJSDomItem.pJS.fn.vendors.destroypJS();
      });
      window['pJSDom'] = [];
    }
  },
};
</script>
