<template>
  <div class="dashboard-homepage">
    <el-container>
      <el-header height="250px" class="homepage-banner">
        <div class="page-header__inner">
          <span class="page-header__logo">
            <svg-icon :icon-class="currentTab.icon" />
          </span>
          <div class="page-header__info-block">
            <h1 class="page-header__title">{{ currentTab.name }}</h1>
            <!-- eslint-disable-next-line -->
            <div class="page-header__sub-title" v-html="currentTab.describe"></div>
          </div>
        </div>
        <flow-nav v-slot="scope" class="homepage-nav" :items="tabs" :active.sync="tabIndex">
          <a class="nav-link" href="#">
            <svg-icon :icon-class="scope.item.icon" />
            {{ scope.item.name }}
          </a>
        </flow-nav>
        <div style="position: absolute; right: 15px; bottom: 15px; z-index: 20">
          <router-link :to="{ name: 'plugins' }">
            <el-button type="text">插件管理</el-button>
          </router-link>
        </div>
      </el-header>
      <el-main>
        <transition name="el-zoom-in-center">
          <component :is="currentTab.view" />
        </transition>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import FlowNav from '@/components/FlowNav';
import { DASHBOARD_TYPE } from '@/constant/designer';

import DashboardCentre from '@/views/dashboard';
import VisualizationCentre from '@/views/visualization';
import MyComponentsCentre from '@/views/myComponents';

export default {
  components: {
    FlowNav,
    DashboardCentre,
    VisualizationCentre,
    MyComponentsCentre,
  },
  data() {
    return {
      tabs: [
        {
          id: 1,
          name: '看板中心',
          icon: 'example',
          view: 'DashboardCentre',
          describe:
            '看板的统一管理（预览、查找、删除）<br/>看板：分为' +
            DASHBOARD_TYPE.map((t) => `<b>${t.label}</b>看板`).join('和') +
            '<div style="padding-left: 20px;">' +
            DASHBOARD_TYPE.map((t) => `${t.label}： ${t.desc}`).join('<br/>') +
            '</div>',
        },
        {
          id: 0,
          name: '可视化管理',
          icon: 'dashboard',
          view: 'VisualizationCentre',
          describe: '<b>管理看板、可视化项目&目录</b><br/>一个可视化由多个看板组成，通过可视化可对看板进行拼接、轮播等展示',
        },
        {
          id: 2,
          name: '我的组件',
          icon: 'component',
          view: 'MyComponentsCentre',
          describe: '组件的来源主要有三个来源：<br/>1、系统默认自带 <br/>2、用户在系统内设计保存<br/>3、用户导入组件库',
        },
      ],

      tabIndex: 0,
    };
  },
  computed: {
    currentTab() {
      return this.tabs[this.tabIndex];
    },
  },
};
</script>
<style lang="stylus">
.dashboard-homepage {
  display: flex;
  height: 100%;
  background: #fff;

  >.el-container>.el-main {
    padding: 0;
  }

  .homepage-banner {
    position: relative;
    background: url('~@/assets/images/designer/demo.png') no-repeat;
    background-position: 300px -50px;
    background-color: #1e1f4c;
    background-size: cover;
    box-shadow: inset 0 0 200px 120px #111129;

    .page-header__inner {
      -webkit-box-flex: 1;
      -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
      flex-grow: 1;
      display: flex;
      margin: 32px;
      color: #ddd;

      .page-header__logo {
        margin: 14px 16px;

        > .svg-icon {
          font-size: 48px;
          vertical-align: middle;
          display: inline-block;
          margin-bottom: 2px;
          fill: currentcolor;
          margin-top: 14px;
        }
      }

      .page-header__title {
        font-size: 24px;
        margin-bottom: 1px;
        padding-top: 6px;
      }

      .page-header__sub-title {
        color: #7b8087;
      }
    }

    .homepage-nav {
      position: absolute;
      left: 0;
      bottom: 15px;
      z-index: 10;
    }
  }
}
</style>
