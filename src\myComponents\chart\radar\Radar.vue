<template>
  <div style="width: 100%; height: 100%">
    <v-chart :option.sync="option" :theme="context.coreData.appearance.mode" :autoresize="true" style="width: 100%; height: 100%" />
  </div>
</template>

<script>
import { optionLoader } from '@/preset/chart';
import dataSource from '@/mixins/dataSource';
import { extend } from '@/utils';

/** 雷达图面板 */
export default {
  inject: ['context'],
  mixins: [dataSource],
  props: {
    wrapped: Boolean,
    // dataset: Object,
    dataset: {
      type: Object,
      default() {
        return {
          dataSource: dataSource.creteDataSourceModel(),
          option: {
            legend: {
              data: [],
            },
            radar: {
              // shape: 'circle',
              indicator: [{ name: '数据1' }, { name: '数据2' }, { name: '数据3' }],
            },
          },
          field: {
            category: 'name',
            value: 'value',
          },
        };
      },
    },
  },
  computed: {
    option() {
      const {
        custom,
        option,
        dataSource: {
          result: { multiple },
        },
        field: { category, value },
      } = this.dataset;

      const { legend, radar } = option;

      const result = {
        legend: {
          data: [],
        },
        radar: {
          indicator: [],
        },
        series: [
          {
            data: [],
          },
        ],
      };

      try {
        if (multiple) {
          this.data.forEach((d, i) => {
            result.series[0].data.push({
              value: d.map((d) => d[value]),
              name: legend.data[i] || '',
            });
          });
        } else {
          result.series[0].data.push({
            value: this.data.map((d) => d[value]),
            name: legend.data[0] || '',
          });
        }

        if (custom) {
          result.radar.indicator = radar.indicator;
        } else {
          result.radar.indicator = (multiple ? this.data[0] : this.data).map((d) => ({ name: d[category] }));
        }
      } catch (e) {
        console.error('雷达图数据处理失败，请检查数据格式');
      }

      return extend(true, optionLoader.radar(this.context.coreData), option, result);
    },
  },
  mounted() {
    this.load();
  },
};
</script>
