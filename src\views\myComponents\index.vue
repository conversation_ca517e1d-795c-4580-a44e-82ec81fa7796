<template>
  <el-tabs v-model="activeName" class="myComponents-centre" tab-position="left" type="border-card" style="height: 100%">
    <el-tab-pane name="myComponents">
      <span slot="label">
        <i class="el-icon-monitor"></i>
        系统组件
      </span>
      <p v-if="prompted">
        <el-alert title="检测到有操作未生效，请及时刷新页面" type="info" @close="prompted = false"></el-alert>
      </p>
      <el-form ref="form" :model="form" label-width="80px" inline size="mini">
        <el-form-item label="组件名称" clearable>
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="是否启用">
          <el-select v-model="form.enable" placeholder="请选择" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" clearable>
          <el-input v-model="form.desc" clearable></el-input>
        </el-form-item>
        <el-form-item style="float: right">
          <el-button type="primary" @click="loadMyComponentsData">搜索</el-button>
          <el-button @click="resetMyComponentsFilter">重置</el-button>
        </el-form-item>
      </el-form>
      <div style="padding: 0 10px">
        <el-button type="info" size="mini" plain :disabled="myComponentsSelection.length !== 1" @click="handleEditButtonClick">编辑</el-button>
        <el-button type="danger" size="mini" plain :disabled="!myComponentsSelection.length > 0" @click="handleDeleteButtonClick">删除</el-button>

        <el-upload
          action=""
          accept=".dat"
          :show-file-list="false"
          :auto-upload="false"
          :on-change="handleImportComponents"
          style="display: inline; margin: 0 10px"
        >
          <el-button type="primary" size="mini" icon="el-icon-upload2"></el-button>
        </el-upload>
        <el-button type="primary" size="mini" icon="el-icon-download" @click="handleDownloadButtonClick"></el-button>

        <sau-dialog title="组件信息" :visible.sync="dialogVisible" :force-middle="true" width="500px">
          <el-form ref="form" :model="dialog" label-width="80px" size="mini">
            <el-form-item label="组件名称" clearable>
              <el-input v-model="dialog.name"></el-input>
            </el-form-item>
            <el-form-item label="是否启用">
              <el-select v-model="dialog.enable" placeholder="请选择" clearable>
                <el-option label="全部" value=""></el-option>
                <el-option label="启用" value="1"></el-option>
                <el-option label="禁用" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="dialog.desc" type="textarea" :rows="5" placeholder="请输入内容" clearable></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" size="mini" @click="submitDialog">提交</el-button>
            <el-button size="mini" @click="dialogVisible = false">取消</el-button>
          </span>
        </sau-dialog>
      </div>
      <el-table :data="myComponents" style="width: 100%" @selection-change="handleComponentsSelectionChange">
        <el-table-column type="expand">
          <template slot-scope="props">
            <el-form label-position="left">
              <el-form-item label="可编辑">
                <span>{{ props.row.editable === '1' ? '是' : '否' }}</span>
              </el-form-item>
              <el-form-item label="可见性">
                <span>{{ props.row.visible === '1' ? '是' : '否' }}</span>
              </el-form-item>
              <el-form-item label="创建时间">
                <span>{{ props.row.createTime }}</span>
              </el-form-item>
              <el-form-item label="更新时间">
                <span>{{ props.row.updateTime }}</span>
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column prop="name" label="组件名称"></el-table-column>
        <el-table-column prop="enable" label="启用" width="100">
          <template slot-scope="scope">
            <el-switch :value="scope.row.enable === '1'" @change="handleChangeMyComponentEnable(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="width" label="初始宽度" width="100"></el-table-column>
        <el-table-column prop="height" label="初始高度" width="100"></el-table-column>
        <el-table-column prop="wrapped" label="主题面板" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.wrapped === '1'"><i class="el-icon-check"></i></el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="desc" label="备注" width="180"></el-table-column>
      </el-table>
    </el-tab-pane>
    <el-tab-pane>
      <span slot="label">
        <i class="el-icon-user"></i>
        自定义组件
      </span>

      <el-table
        :data="defaultComponents"
        style="width: 100%"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        row-key="type"
      >
        <el-table-column prop="name" label="组件名称"></el-table-column>
        <el-table-column prop="width" label="初始宽度" width="100"></el-table-column>
        <el-table-column prop="height" label="初始高度" width="100"></el-table-column>
        <el-table-column prop="wrapped" label="主题面板" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.wrapped"><i class="el-icon-check"></i></el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
    <el-tab-pane>
      <span slot="label">
        <i class="el-icon-box"></i>
        组件库
      </span>
      <el-table :data="libComponents" style="width: 100%">
        <el-table-column type="index" width="50"></el-table-column>
        <el-table-column prop="name" label="组件名称"></el-table-column>
        <el-table-column prop="width" label="初始宽度" width="100"></el-table-column>
        <el-table-column prop="height" label="初始高度" width="100"></el-table-column>
        <el-table-column prop="wrapped" label="主题面板" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.wrapped"><i class="el-icon-check"></i></el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { defaultFolder, libComponents } from '@/myComponents';

import { deleteMyComponents, exportDataFile, getViewComponetsPage, importDataFile, updateViewComponet, updViewComponet } from '@/api/myComponents';
import { clonePure } from '@/utils';

export default {
  name: 'MyComponentsCentre',
  data() {
    return {
      activeName: 'myComponents',
      defaultComponents: defaultFolder.children,
      myComponents: [],
      libComponents,

      form: {
        name: '',
        enable: '',
        desc: '',
      },

      // 组件内容更改提示
      prompted: false,

      dialogVisible: false,
      dialog: {},

      myComponentsSelection: [],
    };
  },
  watch: {
    activeName: {
      immediate: true,
      handler: function (value) {
        if (value === 'myComponents') {
          this.loadMyComponentsData();
        }
      },
    },
  },
  methods: {
    loadMyComponentsData() {
      getViewComponetsPage(this.form)
        .validate('加载我的组件')
        .then(({ data }) => {
          this.myComponents = data;
        });
    },
    resetMyComponentsFilter() {
      this.form = {
        name: '',
        enable: '',
        desc: '',
      };
    },

    handleComponentsSelectionChange(selection) {
      this.myComponentsSelection = selection;
    },
    handleChangeMyComponentEnable(component) {
      component.enable = component.enable === '1' ? '0' : '1';
      updViewComponet(component)
        .validate('切换启用状态')
        .then(() => {
          this.handleDataChanged();
        });
    },
    submitDialog() {
      updateViewComponet(this.dialog)
        .validate('修改组件')
        .then(() => {
          this.$message.success('修改组件信息成功');
          this.dialogVisible = false;
          this.handleDataChanged();
        });
    },
    handleDataChanged() {
      if (!this.prompted) {
        this.$confirm('该操作需要刷新页面后才能生效，是否需要刷新？', '提示', {
          confirmButtonText: '刷新',
          cancelButtonText: '不再提示',
          type: 'warning',
        })
          .then(() => {
            window.location.reload();
          })
          .catch(() => {
            this.loadMyComponentsData();
            this.prompted = true;
          });
      } else {
        this.loadMyComponentsData();
      }
    },
    handleImportComponents(file) {
      importDataFile(file.raw)
        .validate()
        .then(() => {
          this.$message.success('导入数据成功');
          this.handleDataChanged();
        });
    },
    handleDownloadButtonClick() {
      try {
        exportDataFile(this.myComponentsSelection.map((n) => n.id));
      } catch (e) {
        this.$message.success('修改组件信息成功');
        this.dialogVisible = false;
        this.handleDataChanged();
      }
    },
    handleEditButtonClick() {
      if (this.myComponentsSelection.length === 1) {
        this.dialog = clonePure(this.myComponentsSelection[0]);
        this.dialogVisible = true;
      } else {
        this.$message.info('请选择一项记录');
      }
    },
    handleDeleteButtonClick() {
      if (this.myComponentsSelection.length > 0) {
        this.$confirm('此操作将永久删除该记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            deleteMyComponents(this.myComponentsSelection)
              .validate('删除我的组件')
              .then(() => {
                this.$message.success('成功删除记录');
                this.handleDataChanged();
              });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除',
            });
          });
      } else {
        this.$message.info('请选择一项记录');
      }
    },
  },
};
</script>

<style lang="stylus" scoped>
.myComponents-centre {
  box-sizing: border-box;

  >>> .el-tabs__nav.is-left {
    padding-top: 30px;

    .el-tabs__item.is-left {
      width: 160px;
      text-align: center;
    }
  }

  >>> .el-tabs__content {
    height: 100%;
    overflow: auto;
    box-sizing: border-box;
  }
}
</style>
