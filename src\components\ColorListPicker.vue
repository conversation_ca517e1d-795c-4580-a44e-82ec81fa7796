<template>
  <div class="sau-color-list-picker">
    <el-popover placement="left" width="200" trigger="click">
      <div slot="reference" class="container">
        <div v-for="(item, i) in value" :key="i" class="item" :style="{ backgroundColor: item }"></div>
      </div>

      <div>
        <div v-for="(item, i) in value" :key="i" class="item-picker">
          <el-color-picker v-model="value[i]"></el-color-picker>
          <div class="btn-remove" @click="value.splice(i, 1)">-</div>
        </div>
        <div class="btn-add" @click="value.push('#000')">+</div>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'ColorListPicker',

  props: {
    value: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
};
</script>

<style lang="stylus" scoped>
.container {
  text-align: left;
  line-height: 0;
  min-height: 30px;
  cursor: pointer;

  .item {
    display: inline-block;
    width: 30px;
    height: 30px;
  }
}

.item-picker {
  position: relative;
  display: inline-block;
  margin: 5px;
  width: 30px;
  height: 30px;
  vertical-align: middle;

  .btn-remove {
    margin: 0;
    position: absolute;
    right: -3px;
    top: -8px;
    width: 14px;
    height: 14px;
    border-radius: 9px;
    border-color: #ddd;
    line-height: 12px;
  }
}

.btn-add, .btn-remove {
  position: relative;
  margin: 10px;
  line-height: 14px;
  display: inline-block;
  width: 14px;
  height: 14px;
  background: #fff;
  text-align: center;
  border: 1px solid #999;
  border-radius: 2px;
  cursor: pointer;

  &:hover {
    background: #ccc;
  }
}
</style>
