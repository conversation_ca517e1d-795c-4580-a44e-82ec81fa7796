<template>
  <el-drawer title="看板设置" size="70%" class="designer-drawer-setting" :visible.sync="drawer" :direction="direction" v-on="$listeners">
    <SettingTabs :dashboard="dashboard" :logs="logs" />
  </el-drawer>
</template>

<script>
import SettingTabs from './SettingTabs';

export default {
  name: 'SettingDrawer',
  components: { SettingTabs },
  props: {
    dashboard: {
      type: Object,
      default() {
        return {};
      },
    },
    logs: {
      type: Array,
      default() {
        return [];
      },
    },
    panels: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      drawer: false,
      direction: 'rtl',
    };
  },
  methods: {
    open() {
      this.drawer = true;
    },
  },
};
</script>

<style lang="stylus" scoped>
>>> .el-drawer__body {
  height: calc(100% - 77px);

  .el-tabs__content {
    height: 100%;
    overflow: auto;
  }
}
</style>
