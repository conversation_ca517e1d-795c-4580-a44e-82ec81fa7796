import Command from '../Command';

class PasteCommand extends Command {
  name = '粘贴';

  execute() {
    const selection = this.app.getSelection();

    this.app.deleteSelection(selection);
    this.app.addSelection(this.app.clipboard.data);

    if (this.app.clipboard.once) {
      this.app.clipboard.data = null;
      this.app.clipboard.once = false;
    }

    this.cache = {
      original: selection,
      clipboard: this.app.clipboard,
    };

    return true;
  }

  undo() {
    if (this.cache) {
      this.app.deleteSelection(this.cache.clipboard);
      this.app.addSelection(this.cache.original);
    }
    return true;
  }

  redo() {
    if (this.cache) {
      this.app.deleteSelection(this.cache.original);
      this.app.addSelection(this.cache.clipboard);
    }
    return true;
  }
}

export default PasteCommand;
