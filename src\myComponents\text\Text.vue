<template>
  <div class="dashboard-text" :class="dataset.className" :style="dataset.style">{{ dataset.text }}</div>
</template>

<script>
export default {
  name: 'DashboardText',
  props: {
    title: {
      type: String,
      default: '',
    },
    dataset: {
      type: Object,
      default() {
        return {
          text: '默认的文本内容',
          fit: false,
          className: '',
          style: {
            fontFamily: "Times, 'Times New Roman', Georgia, serif",
            fontSize: '14px',
            lineHeight: '14px',
            color: '#000',
            fontWeight: 'normal',
            letterSpacing: '0px',
            textAlign: 'left',
          },
        };
      },
    },
  },
};
</script>
