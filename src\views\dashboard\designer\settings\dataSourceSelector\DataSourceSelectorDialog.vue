<template>
  <sau-dialog title="数据源配置" :visible.sync="visible">
    <div v-loading="loading" element-loading-text="数据加载中" style="height: 100%">
      <el-form ref="form" :model="form" label-width="80px" label-suffix="：">
        <el-row>
          <el-col :span="8">
            <el-form-item label="启用">
              <el-switch v-model="form.enable"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="数据源类型" label-width="100px">
              <el-select v-model="form.type" placeholder="请选择类型" style="width: 100%">
                <el-option label="固定数据" value="0" disabled></el-option>
                <el-option label="HTTP接口" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="接口url">
          <el-input v-model="form.url.value">
            <template slot="prepend">
              <el-dropdown placement="bottom" @command="(type) => (form.url.type = type)">
                <span class="el-dropdown-link">{{ form.url.type }}</span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="GET">GET</el-dropdown-item>
                  <el-dropdown-item command="POST">POST</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item style="text-align: right">
          <el-button type="primary" @click="testApi">测试</el-button>
          <!--        <el-button>重置</el-button>-->
        </el-form-item>
      </el-form>
      <div class="result">
        <el-card shadow="never">
          <div slot="header" class="clearfix">
            <span>响应结果</span>
            <el-dropdown style="float: right" @command="(type) => (form.result.type = type)">
              <span class="el-dropdown-link">
                {{ form.result.type || '原始数据' }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="">原始数据</el-dropdown-item>
                <el-dropdown-item command="JSON">JSON</el-dropdown-item>
                <el-dropdown-item command="XML" disabled>XML</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div>
            <code v-if="!form.result.type">
              {{ JSON.stringify(result) }}
            </code>
            <el-table
              v-else-if="form.result.type === 'JSON'"
              :data="tableResult"
              style="width: 100%"
              row-key="id"
              border
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="40"></el-table-column>
              <el-table-column prop="key" label="属性" width="180"></el-table-column>
              <el-table-column label="值">
                <template slot-scope="scope">
                  <el-tag v-if="Array.isArray(scope.row.value)" type="success" size="medium">Array</el-tag>
                  <el-tag v-else-if="typeof scope.row.value === 'object'" size="medium">Object</el-tag>
                  <span v-else>{{ scope.row.value }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
        <div v-show="form.result.path.length > 0">
          <el-divider content-position="left">
            响应筛选
            <i class="el-icon-caret-bottom"></i>
          </el-divider>
          <template v-if="form.result.multiple">
            <p v-for="(item, i) in form.result.path" :key="i">
              <el-breadcrumb separator-class="el-icon-arrow-right">
                <el-breadcrumb-item v-for="(path, j) in item" :key="j">{{ path }}</el-breadcrumb-item>
              </el-breadcrumb>
            </p>
          </template>
          <p v-else>
            <el-breadcrumb separator-class="el-icon-arrow-right">
              <el-breadcrumb-item v-for="(path, i) in form.result.path" :key="i">{{ path }}</el-breadcrumb-item>
            </el-breadcrumb>
          </p>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" @click="onsubmit">确 定</el-button>
    </span>
  </sau-dialog>
</template>

<script>
import { extend } from '@/utils';
import request from '@/utils/request';

import dataSource from '@/mixins/dataSource';

export default {
  name: 'DataSourceSelectorDialog',
  data() {
    return {
      visible: false,
      loading: false,
      form: dataSource.creteDataSourceModel(),
      result: [],
    };
  },
  computed: {
    tableResult() {
      return this.translateToTableData(this.result);
    },
  },
  methods: {
    show(data) {
      if (data) {
        if (this._dataSource !== data) {
          this.result = [];
          this._dataSource = data;
          this.form = extend(true, dataSource.creteDataSourceModel(), data);
        }
        this.visible = true;
      } else {
        this.$message.warning('无可配置项');
      }
    },
    onsubmit() {
      if (this._dataSource) {
        extend(true, this._dataSource, this.form);
        this._dataSource.result.path = this.form.result.path;
        this.visible = false;
        this.$message.success('完成配置数据源');
      } else {
        this.$message.warning('无可配置数据源');
      }
    },

    testApi() {
      this.loading = true;
      request({
        url: this.form.url.value,
        method: this.form.url.type,
      })
        .then((res) => {
          this.result = res;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },

    buildTableDataItem(key, value, parent, formatId) {
      let id, path;

      if (parent) {
        id = parent.id + formatId(key);
        path = parent.path.concat([key]);
      } else {
        id = formatId(key);
        path = [key];
      }

      const item = {
        id,
        key,
        value: value ?? '',
        path,
      };

      if (value && typeof value === 'object') {
        item.children = this.translateToTableData(value, item);
      }
      return item;
    },
    translateToTableData(data, parent) {
      let tableResult;
      if (data) {
        if (Array.isArray(data)) {
          tableResult = data.map((value, key) => this.buildTableDataItem(key, value, parent, (id) => '[' + id + ']'));
        } else if (typeof this.result === 'object') {
          tableResult = Object.keys(data).map((key) => this.buildTableDataItem(key, data[key], parent, (id) => (parent ? '.' : '') + id));
        }
      }
      return tableResult;
    },
    handleSelectionChange(selection) {
      this.form.result.multiple = selection.length > 1;
      this.form.result.path = selection.length > 0 ? (this.form.result.multiple ? selection.map((item) => item.path) : selection[0].path) : [];
    },
  },
};
</script>
