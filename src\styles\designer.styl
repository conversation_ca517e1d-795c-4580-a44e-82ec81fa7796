// 花浅葱
$primaryColor = '#1E88A8';

.dashboard-designer {
  $headerHeight = 40px;
  $toolbarWidth = 42px;
  $viewCtrlWidth = 250px;
  $bBarHeight = 20px;
  $borderColor = #2B2B2B;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #2B2B2B;
  user-select: none;
  overflow: hidden;

  .designer-header {
    padding: 0px 2.5px;
    height: $headerHeight;
    background-color: #3C3F41;
    line-height: $headerHeight;
    border-bottom: 1px solid $borderColor;
    text-align: center;
    color: #cacfd6;

    .d-button {
      margin: 0 5px;

      &[disabled='disabled'] {
        color: #bdbdbd;
        background-color: #313131;
        cursor: no-drop;
      }
    }

    .design-btn-menu {
      margin: 0px;
    }

    .btn-refresh {
      margin-right: 50px;

      &:hover {
        animation: rotating 2s linear infinite;
      }
    }
  }

  .designer-main {
    padding: 0 $viewCtrlWidth $bBarHeight $toolbarWidth;
    flex: 1;
    position: relative;
    height: 100%;
    background: url('~@/assets/images/designer/back_p.png') repeat;
    overflow: hidden;

    .canvas-wrapper {
      text-align: center;
    }

    .design-context {
      margin: 50px;
      text-align: left;
      background: #FFF;
    }

    // 右键菜单
    .design-context > div.fixed {
      z-index: 1024;
    }

    .design-view {
      position: relative;
      box-shadow: 0px 0px 8px 0px #555;
    }

    > div[class|=designer] {
      position: absolute;
      background-color: #3C3F41;
      border-color: $borderColor;
    }
  }

  .designer-toolbar {
    top: 0;
    left: 0;
    height: 100%;
    width: $toolbarWidth;
    box-sizing: border-box;
    border-right: 1px solid;

    .d-button.mini {
      display: block;
      margin: 10px auto;
    }

    .el-divider {
      margin: 10px 0;
      background-color: #636363;
    }
  }

  .designer-bbar {
    bottom: 0;
    left: 0;
    right: $viewCtrlWidth;
    height: $bBarHeight;
    border-top: 1px solid;
  }

  .designer-view-ctrl {
    top: 0;
    right: 0;
    height: 100%;
    width: $viewCtrlWidth;
    border-left: 1px solid;
    word-break: break-all;
    color: #eee;

    .sau-panel {
      margin: 10px 0;
      min-height: auto;
      border: 0;
      border-bottom: 1px solid #636363;
      background: #3C3F41;

      .sau-panel--header {
        background: #D7D7D7;
        height: 20px;
        line-height: 20px;
        text-align: left;

        .sau-panel--toolbar .toolbar-btn {
          padding: 0px;
          border: 0;
        }
      }

      .sau-panel--title {
        color: #000;
        line-height: inherit;
      }

      .sau-panel--body {
        padding: 10px 0px;
        background: none;
      }
    }

    .el-collapse, .el-collapse-item__header, .el-collapse-item__wrap {
      background: transparent;
      color: inherit;
      border-color: #666;
    }

    .el-collapse-item__header {
      padding-left: 10px;
      /* font-weight: bolder; */
    }

    .el-collapse-item__content {
      padding: 10px 0;
      color: #eee;
      background: #343638;
    }

    .el-collapse-item__header.is-active {
      border-bottom-color: transparent;
    }
  }

  .dashboard-layout {
    position: relative;
    transform-origin: left top;
  }
}

.designer-drawer-setting {
  .el-drawer__body>.el-tabs .el-tabs__content {
    height: 100%;
    box-sizing: border-box;
    user-select: text;
    overflow: auto;
  }

  .log-pane {
    .el-table__cell {
      padding: 5px 0;
    }

    .el-table__expand-column>.cell {
      display: none;
    }

    .expandable .el-table__expand-column>.cell {
      display: block;
    }
  }
}

.designer-toolbar {
  padding: 5px 3px;
  text-align: center;
}

.design-view {
  .box {
    box-shadow: 0 0 4pt rgba(0, 0, 0, 0.25);
    border-radius: 20pt;
    background-color: rgba(255, 255, 255, 0.25);
    user-select: none;
    cursor: context-menu;
  }

  .vdr {
    touch-action: none;
    border: 0;

    &.active {
      border: 1px dashed #d6d6d6;
      z-index: 99999999 !important;
    }
  }

  .vue-resizable-handle, .handle {
    z-index: 99999999;
  }

  .ref-line {
    box-sizing: border-box;
    border: 0.5px #47a1fa;
    border-style: dashed;
    background: none;
  }
}

.dashboard-tools {
  position: fixed;
  top: 10px;
  right: 10px;
  padding: 0px;
  line-height: 1;
  z-index: 999;
  cursor: pointer;

  > * {
    visibility: hidden;
  }

  .el-link {
    padding: 5px;
    font-size: 20px;
    color: #fff;

    .svg-icon {
      font-size: 18px;
    }

    &:hover {
      background: #cccccc2b;
    }
  }

  &:hover {
    background: #eeeeee2b;

    > * {
      visibility: visible;
    }
  }
}

.designer-state-highlight {
  background: #2d2d2d;
}

.designer-bbar {
  padding: 0 5px;
  color: #ccc;
  font-size: 10px;
  line-height: 20px;
  text-align: center;
  overflow: hidden;

  .el-slider {
    display: inline-block;
    margin-right: 10px;
    width: 100px;
    vertical-align: middle;

    .el-slider__runway {
      margin: 0;
      height: 2px;

      .el-slider__bar {
        height: 2px;
      }

      .el-slider__button-wrapper {
        width: 32px;
        height: 32px;

        .el-slider__button {
          width: 10px;
          height: 10px;
        }
      }
    }
  }

  .bbar-item {
    margin: 0 5px;
  }

  .designer-bbar-position {
    position: relative;
    top: 1px;
    display: inline-block;
    width: 60px;
  }
}

.designer-view-ctrl {
  >.ps.ps-container {
    height: 100%;
  }
}

.designer-properties {
  padding-bottom: 10px;
  text-align: center;

  label.el-form-item__label {
    color: #eee;
  }

  .el-form-item {
    padding: 0 5px;
  }

  .el-divider {
    margin: 10px 0;
    background-color: #636363;
  }

  .el-upload-list__item {
    transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);

    >div {
      height: 100%;

      >.el-upload-list__item-thumbnail {
        object-fit: contain;
        object-position: center;
      }
    }
  }

  .uploaded .el-upload {
    display: none;
  }

  .el-divider__text {
    background-color: #3c3f41;
    color: #ffffff;
  }

  .el-color-picker {
    padding-left: 10%;
    width: 80%;

    .el-color-picker__trigger {
      width: 100%;
      padding: 0;
      border: 0;

      .el-color-picker__color {
        border-radius: 0;
      }
    }
  }

  .properties-compoenent, .properties-default {
    margin-top: 20px;
    color: #fff;
    text-align: center;
  }

  .normal-text {
    text-align: left;
  }

  .panel-id {
    text-align: left;
    margin: 0;
    padding: 5px;
    color: #888;
    font-size: 12px;
  }

  .efficiency {
    p {
      text-align: left;
      padding-left: 10px;
      margin-top: 0;
      color: #969696;
      font-size: 12px;
    }
  }
}

.designer-components-list {
  .sau-panel {
    margin: 10px;
    border: 1px solid #515da9;
    border-radius: 5px;
    background: #1b1f38;
  }

  .el-link {
    color: inherit;

    i.el-icon-collection-tag {
      font-size: 10px;
      color: #999;
    }
  }

  .designer-filter {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10px;
    width: 100%;
    box-sizing: border-box;
    background: #3c3f41;
  }

  .designer-list {
    margin-top: 52px;
    border-right: 0;

    .el-submenu .el-submenu {
      .el-submenu__title {
        background-color: #343638 !important;
        border-bottom-width: 0;
      }
    }

    .el-menu-item {
      cursor: default;
      overflow: hidden;

      &:hover {
        // background-color: rgba(0, 0, 0, 0.08);
        .designer-list-item-tools {
          right: 0;
        }
      }
    }

    .el-submenu__title {
      border-bottom-width: 1px;
      border-bottom-style: solid;
      border-bottom-color: #666 !important;
      background: #3c3f41 !important;
      font-weight: bold;
    }
  }
}

.designer-layers {
  padding-top: 48px;

  .designer-layers-filter {
    position: absolute;
    padding: 10px;
    top: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    background: #3C3F41;
    z-index: 10;
  }

  .designer-layers-tree {
    background: none;
    color: #fff;
  }

  .el-tree-node.is-checked {
    background: #333;
  }

  .el-tree-node__content:hover, .el-tree-node:focus > .el-tree-node__content {
    background-color: #5c6161;
  }

  .el-tree-node__content {
    padding-right: 5px;
    border-bottom: 1px solid #666;
  }

  .tree-node {
    display: flex;
    flex: 1;
    justify-content: space-between;

    // .tools-left {
    // border: 0;
    // border-left: 1px solid #000;
    // padding-left: 5px;
    // }
    > .text {
      padding: 0 5px;
      flex: 1;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .square {
    display: inline-block;
    margin-left: 5px;
    padding: 0 2px;
    border: 1px solid #666;
    border-radius: 5px;
    background: #333;
    line-height: 18px;
    font-size: 12px;
    text-align: center;
  }

  .node-visible {
    width: 13px;
    height: 17px;
  }

  .node-select {
    float: right;
  }
}
