import Command from '@/mixins/command/Command';
import { cloneDeep } from '@/utils';

class PasteCommand extends Command {
  name = '粘贴';

  execute() {
    const selection = this.app.getSelection();
    const clipboard = cloneDeep(this.app.clipboard.data);

    this.cache = {
      original: selection,
      clipboard,
    };

    this.app.deleteContent(selection);
    this.app.addContent(clipboard);

    if (this.app.clipboard.once) {
      this.app.clipboard.data = null;
      this.app.clipboard.once = false;
    }

    return true;
  }

  undo() {
    if (this.cache) {
      this.app.deleteContent(this.cache.clipboard);
      this.app.addContent(this.cache.original);
    }
    return true;
  }

  redo() {
    if (this.cache) {
      this.app.deleteContent(this.cache.original);
      this.app.addContent(this.cache.clipboard);
    }
    return true;
  }
}

export default PasteCommand;
