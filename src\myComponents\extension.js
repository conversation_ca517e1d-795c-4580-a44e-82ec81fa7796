let extensions = [];

export function getPropertiesExtension(type) {
  return extensions.filter((n) => n.type === undefined || n.type === type).map((item) => item.handler);
}

export function registerPropertiesExtension(type, handler) {
  if (type) {
    if (typeof handler === 'function') {
      extensions.push({ type, handler });
    } else if (typeof type === 'function') {
      extensions.push({ handler: type });
    } else {
      extensions.push({
        handler() {
          return type;
        },
      });
    }
  } else {
    throw new Error('不合规的属性扩展');
  }
}

export function clearPropertiesExtension() {
  extensions = [];
}
