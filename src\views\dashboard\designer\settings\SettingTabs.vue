<template>
  <el-tabs ref="borderCard" tab-position="left" type="border-card" style="height: 100%">
    <el-tab-pane label="常规">
      <Regular :dashboard="dashboard" />
    </el-tab-pane>
    <el-tab-pane label="播放器">
      <el-form :model="dashboard" label-suffix="：" label-width="120px">
        <el-form-item label="工具条">
          <el-switch v-model="dashboard.tools.enable"></el-switch>
        </el-form-item>
      </el-form>
    </el-tab-pane>
    <el-tab-pane label="控制台">
      <LogPane :logs="logs"></LogPane>
    </el-tab-pane>
    <el-tab-pane v-if="development" label="数据模型">
      <code>
        <pre>{{ JSON.stringify(dataModel, null, 2) }}</pre>
      </code>
    </el-tab-pane>
    <el-tab-pane v-if="development" label="权限">
      <Authority />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import { setVersion } from '@/utils/version';

import develop from '@/mixins/develop';
import Regular from './tabs/Regular';
import Authority from './tabs/Authority';
import LogPane from './tabs/LogPane';

export default {
  name: 'SettingTabs',
  components: {
    // Alarm,
    LogPane,
    Regular,
    Authority,
  },
  inject: ['context'],
  mixins: [
    // board,
    develop,
  ],
  props: {
    dashboard: {
      type: Object,
      default() {
        return {};
      },
    },
    logs: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  computed: {
    dataModel() {
      return this.context.getDataModel();
    },
  },
  mounted() {
    if (this.dashboard.version) {
      setVersion.call(this.$refs.borderCard.$refs.nav.$el, this.dashboard.version, (version) => (this.dashboard.version = version));
    }
  },
};
</script>

<style scoped>
>>> .dashboard-version {
  position: absolute;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #ccc;
  cursor: pointer;
}
</style>
