/**
 * @Description: 日志记录
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/10
 */
import SauUI from 'sau-ui';

const dayjs = SauUI.lib.dayjs;

class LogInfo {
  message;
  detail = '';
  level;
  time;

  constructor(message, level, time = dayjs().format('YYYY-MM-DD HH:mm:ss')) {
    this.message = message || '-';
    this.level = level || 'info';
    this.time = time;
  }
}

export default {
  data() {
    return {
      logs: [], // 操作日志
    };
  },
  methods: {
    log(info, level = 'info') {
      // 最多存放500条日志
      if (this.logs.length > 500) {
        this.logs.shift();
      }

      let logInfo;

      if (Boolean(info) && typeof info === 'object') {
        logInfo = new LogInfo(info.message, info.level);
        logInfo.detail = info.detail;
      } else {
        logInfo = new LogInfo(info, level);
      }

      this.logs.push(logInfo);
    },
  },
};
