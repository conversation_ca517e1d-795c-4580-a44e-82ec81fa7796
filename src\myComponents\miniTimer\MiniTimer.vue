<template>
  <span v-if="Array.isArray(dataset.format)" :style="dataset.style">
    <span v-for="(item, i) in dataset.format" :key="i" :style="item.style">
      {{ item.format ? toFormat(item.format) : item.text }}
    </span>
  </span>
  <span v-else :style="dataset.style">{{ timeString }}</span>
</template>

<script>
import SauUI from 'sau-ui';

const dayjs = SauUI.lib.dayjs;

// import * as dayjs from 'dayjs';
// import 'dayjs/locale/zh-cn';
// import duration from 'dayjs/plugin/duration';
//
// dayjs.extend(duration);
// dayjs.locale('zh-cn'); // use loaded locale globally

export default {
  name: 'MiniTimer',
  props: {
    dataset: {
      type: Object,
      default() {
        return {
          startTime: '',
          placeholder: 0, // 为空的占位符 如0月0日
          format: 'YYYY-MM-DD HH:mm:ss',
          style: {
            fontFamily: "Times, 'Times New Roman', Georgia, serif",
            fontSize: '14px',
            lineHeight: '14px',
            color: '#000',
            fontWeight: 'normal',
            letterSpacing: '0px',
            textAlign: 'left',
          },
        };
      },
    },
  },
  data() {
    return {
      timeString: '',
      timeCore: null,
    };
  },
  created() {
    this._timer = setInterval(() => {
      //      开始时间    时间格式  开始时间格式
      const { startTime, format, startTimeFormat } = this.dataset;

      let startTimeValue;

      if (startTime) {
        // 如果格式化是自定义格式化，则开始时间必须为数字或者有开始时间格式化模版
        if (Array.isArray(format)) {
          if (this.isNumber(startTime)) {
            startTimeValue = startTime;
          } else if (typeof startTime === 'string' && typeof startTimeFormat === 'string') {
            startTimeValue = dayjs(startTime, startTimeFormat);
          }
        } else if (typeof startTime === 'string' && typeof format === 'string') {
          // 优先使用开始事件格式化处理
          startTimeValue = dayjs(startTime, typeof startTimeFormat === 'string' ? startTimeFormat : format);
        }
      }

      if (startTimeValue) {
        this.timeCore = dayjs.duration(dayjs().diff(startTimeValue));
      } else {
        this.timeCore = dayjs();
      }

      if (typeof format === 'string') {
        this.timeString = this.timeCore.format(format);
      }
    }, 1000);
  },
  beforeDestroy() {
    clearInterval(this._timer);
  },
  methods: {
    isNumber(value) {
      return typeof value === 'number' && !isNaN(value);
    },
    toFormat(rule) {
      // 将时间分块格式化，加了向上取整和向下取整的模版 如：floorDays 、 ceilDays
      const realRule = rule.replace('floor', 'as').replace('ceil', 'as');
      if (this.timeCore) {
        let result = this.timeCore[realRule]();
        if (rule.startsWith('floor')) {
          result = Math.floor(result);
        } else if (rule.startsWith('ceil')) {
          result = Math.ceil(result);
        }
        return result;
      }
      return this.dataset.placeholder;
    },
  },
};
</script>
