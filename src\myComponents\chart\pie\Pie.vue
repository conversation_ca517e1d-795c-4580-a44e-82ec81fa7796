<template>
  <div style="width: 100%; height: 100%">
    <v-chart :option.sync="option" :autoresize="true" :theme="context.coreData.appearance.mode" style="width: 100%; height: 100%" />
  </div>
</template>

<script>
import { optionLoader } from '@/preset/chart';
import dataSource from '@/mixins/dataSource';
import { extend } from '@/utils';

export default {
  inject: ['context'],
  mixins: [dataSource],
  props: {
    wrapped: Boolean,
    // dataset: Object,
    dataset: {
      type: Object,
      default() {
        return {
          dataSource: dataSource.creteDataSourceModel(),
          option: {
            series: [
              {
                label: {},
              },
            ],
          },
        };
      },
    },
  },
  computed: {
    option() {
      const {
        option,
        dataSource: {
          result: { multiple },
        },
      } = this.dataset;

      let data;

      try {
        data = multiple ? this.data[0] : this.data;
      } catch (e) {
        data = [];
        this.$message.info('检测到数据项变动，请进行数据同步');
      }

      const result = {
        dataset: {
          source: data,
        },
      };

      if (Array.isArray(option.tooltip?.formatter)) {
        result.tooltip = {
          formatter({ marker, seriesName, name, value, percent }) {
            return option.tooltip.formatter.reduce((pre, param) => {
              if (param === 'marker') {
                return pre + marker;
              } else if (param === 'a') {
                return pre + seriesName;
              } else if (param === 'b') {
                return pre + name;
              } else if (param === 'c') {
                return pre + value;
              } else if (param === 'd') {
                return pre + percent;
              } else if (value[param] !== null && value[param] !== void 0) {
                return pre + value[param];
              } else {
                return pre + param;
              }
            }, '');
          },
        };
      }

      return extend(true, optionLoader.pie(this.context.coreData), option, result);
    },
  },
  mounted() {
    this.load();
  },
};
</script>
