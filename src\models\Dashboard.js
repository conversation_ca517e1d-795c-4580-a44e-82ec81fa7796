import Appearance from '@/models/Appearance';

class Dashboard {
  // 基础信息
  id;
  name = '';
  desc = '';
  type = 0; // 看板类型
  size = [1920, 1080];
  fitMode = 1; // 默认使用比例模式，因为比例不涉及缩放
  timezone = 0;
  autoRefresh = 600000; // 自动刷新间隔
  editable = true;
  version = ''; // 看板版本，由系统进行检测
  createTime = '';
  updateTime = '';

  layout = {
    colNum: 12,
    rowHeight: 25,
  };

  // 工具栏
  tools = {
    enable: true,
    // items: [
    //   {
    //     name: 'fullscreen',
    //     value: true,
    //     options: [
    //       {
    //         icon: 'fullscreen',
    //         value: true,
    //         tooltip: '开启全屏',
    //       },
    //       {
    //         icon: 'exit-fullscreen',
    //         value: false,
    //         tooltip: '关闭全屏',
    //       },
    //     ],
    //   },
    // ],
  };

  // 外观样式
  appearance = new Appearance();

  // 自定义
  custom = {
    idPrefix: 'dashboard-panel', // 面板id前缀
    layoutClass: '', // 看板布局class
    style: '', // 高级->自定义样式
  };

  constructor(id) {
    this.id = id;
  }
}

export default Dashboard;
