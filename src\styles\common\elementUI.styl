// .el-tabs--border-card {
// height: 100%;
// }
//
// .el-tabs {
// &.el-tabs--top, &.el-tabs--bottom {
// }
//
// &.el-tabs--right, &.el-tabs--left {
// }
//
// .el-tabs__content {
// height: calc(100% - 40px);
// padding: 0 !important;
// }
// }
// .el-tab-pane {
// height: 100%;
// }

.el-image-viewer__wrapper{
  z-index: 8000 !important;
}

// 取消tab聚焦的效果
.el-tabs__item:focus.is-active.is-focus:not(:active) {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

// el-message消息弹框总是置于最顶层
.el-message {
  z-index: 99999999 !important;
}

.el-message-box__wrapper {
  z-index: 9999 !important;
}

// el-tree-node 留间距
.el-tree-node__children>.el-tree-node:first-child {
  margin-top: 5px;
}

.el-tree-node__children>.el-tree-node:last-child {
  margin-bottom: 5px;
}

.el-zoom-in-right-enter-active, .el-zoom-in-right-leave-active {
  opacity: 1;
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
  transform-origin: right center;
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
}

.el-zoom-in-right-enter, .el-zoom-in-right-leave-active {
  opacity: 0;
  -webkit-transform: scaleX(0);
  transform: scaleX(0);
}
